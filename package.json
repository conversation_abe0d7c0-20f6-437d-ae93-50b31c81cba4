{"name": "knodweb", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.5.1", "@fortawesome/free-solid-svg-icons": "^6.5.1", "@fortawesome/react-fontawesome": "^0.2.0", "@reduxjs/toolkit": "^1.9.5", "@types/node": "20.6.3", "@types/react": "18.2.22", "@types/react-dom": "18.2.7", "aws-amplify": "^5.3.11", "axios": "^1.5.0", "bootstrap": "^5.3.2", "echarts": "^5.4.3", "echarts-for-react": "^3.0.2", "eslint": "8.50.0", "eslint-config-next": "13.5.2", "next": "13.5.2", "react": "18.2.0", "react-bootstrap": "^2.9.0", "react-dom": "18.2.0", "react-icons": "^4.11.0", "react-redux": "^8.1.2", "typescript": "5.2.2"}, "devDependencies": {"encoding": "^0.1.13", "sass": "^1.68.0"}}