import { configureStore } from "@reduxjs/toolkit";
import authReducer from "./slices/authReducer";
import teamReducer from "./slices/teamReducer";
import category from "./slices/categoryReducer";
import dashboard from "./slices/dashboardReducer";
import player from "./slices/playerReducer";
import chart from "./slices/chartReducer";
import eventReducer from "./slices/eventReducer";
import matchReducer from "./slices/matchReducer";

export const store = configureStore({
  reducer: {
    auth: authReducer,
    team: teamReducer,
    category: category,
    dashboard: dashboard,
    player: player,
    chart: chart,
    event: eventReducer,
    match: matchReducer,
  },
  devTools: process.env.NODE_ENV !== "production",
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
