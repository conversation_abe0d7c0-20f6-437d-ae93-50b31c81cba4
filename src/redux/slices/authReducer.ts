import axios from 'axios';
import { redirect } from 'next/navigation';
import { Amplify, Auth } from 'aws-amplify';
import { createSlice } from '@reduxjs/toolkit';
import { createAsyncThunk } from '@reduxjs/toolkit';
import { RootState } from '@/redux/store';
import { get } from '@/api/amplifyApi';
import { USER_MANAGEMENT_SERVICE } from '@/api/apiTypes';
import { PENDING, NOTLOGGED, LOGGED } from '@/const/loginState';
import { clubConfigUrl } from '@/config/urlConfig';

interface SignInData {
  clubId: string;
  email: string;
  password: string;
  rememberMe: boolean;
}

interface Service {
  EVENT_SERVICE: string;
  FOOTBALL_SERVICE: string;
  MESSAGING_SERVICE: string;
  PLAYERMAKER_SERVICE: string;
  USER_MANAGEMENT_SERVICE: string;
}

interface ResetPasswordData {
  clubId: string;
  email: string;
  password: string;
  code: string;
}

interface InitialUserDetails {
  firstName: string;
  profileImageUrl: string;
  id: string;
}

interface AuthState {
  isLoading: boolean;
  userDetails: InitialUserDetails;
  errorMessage: string | null;
  isAmplifyConfigured: boolean;
  loginState: string;
  hasSentResetLink: boolean;
  services: Service;
}

const initialUserDetails = {
  firstName: '',
  profileImageUrl: '',
  id: '',
};

const initialServices = {
  EVENT_SERVICE: '',
  FOOTBALL_SERVICE: '',
  MESSAGING_SERVICE: '',
  PLAYERMAKER_SERVICE: '',
  USER_MANAGEMENT_SERVICE: '',
};

const initialState: AuthState = {
  isLoading: false,
  userDetails: initialUserDetails,
  errorMessage: null,
  isAmplifyConfigured: false,
  loginState: PENDING,
  hasSentResetLink: false,
  services: initialServices,
};

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    setIsLoading: (state, action) => {
      state.isLoading = action.payload;
    },
    setUserDetails: (state, action) => {
      state.userDetails = action.payload;
    },
    setErrorMessage: (state, action) => {
      state.errorMessage = action.payload;
    },
    setIsAmplifyConfigured: (state, action) => {
      state.isAmplifyConfigured = action.payload;
    },
    setHasSentResetLink: (state, action) => {
      state.hasSentResetLink = action.payload;
    },
    setLoginState: (state, action) => {
      state.loginState = action.payload;
    },
    setServices: (state, action) => {
      state.services = action.payload;
    },
  },
});

export const signInAndFetchUser = createAsyncThunk(
  'auth/signInAndFetchUser',
  async ({ clubId, email, password, rememberMe }: SignInData, { dispatch }) => {
    dispatch(setIsLoading(true));

    try {
      const ClubDetailResponse: { data: any } = await axios.get(
        `${clubConfigUrl}?clubId=${clubId.toUpperCase()}`
      );

      const { Auth: auth, API, services } = ClubDetailResponse?.data || {};

      Amplify.configure({ Auth: auth, API, ssr: true });

      await Auth.signIn(email, password);

      if (rememberMe) {
        await Auth.rememberDevice();
      }

      const getUserIdResponse: { data: any } = await get(
        `/api/v1/users/${email}/id`,
        services[USER_MANAGEMENT_SERVICE]
      );

      const { id } = getUserIdResponse?.data;

      const getUserDetailResponse = await get(
        `/api/v1/users/${id}`,
        services[USER_MANAGEMENT_SERVICE]
      );

      dispatch(
        setUserDetails(
          { ...getUserDetailResponse?.data, clubId: clubId.toUpperCase() } || {}
        )
      );

      const { firstName, lastName, profileImageUrl } =
        getUserDetailResponse?.data || {};

      localStorage.setItem('coachId', id);
      localStorage.setItem('clubId', clubId);
      localStorage.setItem('firstName', firstName);
      localStorage.setItem('lastName', lastName);
      localStorage.setItem('profileImageUrl', profileImageUrl);

      dispatch(setServices(services));
      dispatch(setLoginState(LOGGED));
      dispatch(setIsLoading(false));
    } catch (error: any) {
      let errorMessage = 'Login failed!';

      if (error.response && error.response.config.url.includes(clubConfigUrl)) {
        errorMessage = 'Please provide a valid Club ID!';
      } else if (
        error.code === 'UserNotFoundException' ||
        error.code === 'NotAuthorizedException'
      ) {
        errorMessage = 'Incorrect email or password!';
      }

      dispatch(setErrorMessage(errorMessage));
    } finally {
      dispatch(setIsLoading(false));
    }
  }
);

export const configureAmplify = createAsyncThunk(
  'auth/configureAmplify',
  async (_, { dispatch, getState }) => {
    const state = getState() as RootState;
    const clubId = localStorage.getItem('clubId') || '';

    if (!clubId) {
      redirect('/login');
    }

    if (!state.auth.isAmplifyConfigured) {
      try {
        const ClubDetailResponse: { data: any } = await axios.get(
          `${clubConfigUrl}?clubId=${clubId.toUpperCase()}`
        );

        const { Auth: auth, API, services } = ClubDetailResponse?.data || {};

        Amplify.configure({ Auth: auth, API, ssr: true });

        dispatch(setServices(services));
        dispatch(setIsAmplifyConfigured(true));
      } catch (error) {
        redirect('/login');
      }
    }
  }
);

export const signOut = createAsyncThunk(
  'auth/signOut',
  async (_, { dispatch }) => {
    try {
      await dispatch(configureAmplify());

      await Auth.signOut();

      dispatch(setLoginState(NOTLOGGED));
    } catch (error) {
      console.log(error);
    }
  }
);

export const forgotPassword = createAsyncThunk(
  'auth/forgotPassword',
  async (
    { clubId, email }: { clubId: string; email: string },
    { dispatch }
  ) => {
    dispatch(setIsLoading(true));
    localStorage.setItem('clubId', clubId);

    try {
      await dispatch(configureAmplify());

      await Auth.forgotPassword(email);

      dispatch(setErrorMessage('Check your email for the verification code!'));
      dispatch(setHasSentResetLink(true));
    } catch (error: any) {
      if (error.code === 'LimitExceededException') {
        const errorMessage = error.message;
        dispatch(setErrorMessage(errorMessage));
      } else {
        console.log(error);
        dispatch(setErrorMessage('Sending verification code failed!'));
      }
    }

    dispatch(setIsLoading(false));
  }
);

export const forgotPasswordSubmit = createAsyncThunk(
  'auth/forgotPasswordSubmit',
  async (
    { clubId, email, password, code }: ResetPasswordData,
    { dispatch }
  ) => {
    dispatch(setIsLoading(true));
    localStorage.setItem('clubId', clubId);

    try {
      await dispatch(configureAmplify());

      await Auth.forgotPasswordSubmit(email, code, password);

      const rememberMe = false;

      await dispatch(
        signInAndFetchUser({ clubId, email, password, rememberMe })
      );
    } catch (error: any) {
      if (
        error.code === 'CodeMismatchException' ||
        error.code === 'LimitExceededException'
      ) {
        const errorMessage = error.message;
        dispatch(setErrorMessage(errorMessage));
      } else {
        console.log(error);
        dispatch(setErrorMessage('Reseting password failed!'));
      }
    }

    dispatch(setIsLoading(false));
  }
);

export const {
  setServices,
  setIsLoading,
  setLoginState,
  setUserDetails,
  setErrorMessage,
  setIsAmplifyConfigured,
  setHasSentResetLink,
} = authSlice.actions;

export default authSlice.reducer;
