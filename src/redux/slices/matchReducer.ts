import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import { RootState } from "@/redux/store";
import { get, post } from "@/api/amplifyApi";
import { FOOTBALL_SERVICE, EVENT_SERVICE } from "@/api/apiTypes";

interface Opponent {
  _id: string;
  name: string;
}

interface Position {
  _id: string;
  name: string;
  shortName?: string;
}

interface Player {
  userId: string;
  firstName: string;
  lastName: string;
  dateOfBirth: {
    date: number;
    month: number;
    year: number;
  };
  height: number;
  weight: number;
  profileImageUrl: string;
  emailId: string;
  type: string;
  gender: string;
  sportsProfileId: string;
  isAvailable: boolean;
  jersyNo: string;
  givenPosition: string;
  no?: string | number;
  pos?: string;
  name?: string;
  assists?: string | number;
  goals?: string | number | Array<{ id: number; minute: number }>;
  red?: string | number;
  yellow?: string | number;
  subOut?: string | number;
  subIn?: string | number;
  playingTime?: string | number;
  ratings?: string | number;
}

interface TimeEntry {
  id: number;
  time: number;
  type: string;
}

interface MatchActivity {
  _id: string;
  code: string;
  type: string;
}

interface MatchState {
  opponents: Opponent[];
  players: Player[];
  positions: Position[];
  timeEntries: TimeEntry[];
  matchActivities: MatchActivity[];
  isOpponentsLoading: boolean;
  isPlayersLoading: boolean;
  isPositionsLoading: boolean;
  isDownloadingReport: boolean;
  isLoadingMatchStats: boolean;
  selectedOpponent: Opponent | null;
  teamName: string;
  matchDuration: number | null;
}

const initialState: MatchState = {
  opponents: [],
  players: [],
  positions: [],
  timeEntries: [],
  matchActivities: [],
  isOpponentsLoading: false,
  isPlayersLoading: false,
  isPositionsLoading: false,
  isDownloadingReport: false,
  isLoadingMatchStats: false,
  selectedOpponent: null,
  teamName: "",
  matchDuration: null,
};

export const getOpponents = createAsyncThunk(
  "match/getOpponents",
  async ({ opponentIds, page = 1, size = 1 }: { opponentIds: string; page?: number; size?: number }, { dispatch, getState }) => {
    dispatch(setIsOpponentsLoading(true));

    const state = getState() as RootState;
    const services = state.auth.services;

    try {
      const response = await get(`/api/v1/opponents?opponentIds=${opponentIds}&page=${page}&size=${size}`, services[EVENT_SERVICE]);

      const opponentsData = response?.data || [];
      dispatch(setOpponents(opponentsData));

      if (opponentsData.length > 0) {
        dispatch(setSelectedOpponent(opponentsData[0]));
      }

      return opponentsData;
    } catch (error) {
      return [];
    } finally {
      dispatch(setIsOpponentsLoading(false));
    }
  }
);

export const getPlayers = createAsyncThunk(
  "match/getPlayers",
  async (
    { teamId, page = 1, size = 1000, generateImageUrl = false }: { teamId: string; page?: number; size?: number; generateImageUrl?: boolean },
    { dispatch, getState }
  ) => {
    dispatch(setIsPlayersLoading(true));

    const state = getState() as RootState;
    const services = state.auth.services;

    try {
      const response = await get(`/api/v1/teams/${teamId}/players?page=${page}&size=${size}&generateImageUrl=${generateImageUrl}`, services[FOOTBALL_SERVICE]);

      const playersData = response?.data?.data || [];

      const mappedPlayers = playersData.map((player: any) => ({
        ...player,
        no: player.jersyNo || "",
        pos: player.givenPosition || "",
        name: `${player.firstName || ""} ${player.lastName || ""}`,
        assists: "",
        goals: "",
        red: "",
        yellow: "",
        subOut: "",
        subIn: "",
        playingTime: "",
        ratings: "",
      }));

      dispatch(setPlayers(mappedPlayers));

      return mappedPlayers;
    } catch (error) {
      return [];
    } finally {
      dispatch(setIsPlayersLoading(false));
    }
  }
);

export const getPositions = createAsyncThunk("match/getPositions", async (_, { dispatch, getState }) => {
  dispatch(setIsPositionsLoading(true));

  const state = getState() as RootState;
  const services = state.auth.services;

  try {
    const response = await get(`/api/v1/positions`, services[FOOTBALL_SERVICE]);

    const positionsData = response?.data || [];
    dispatch(setPositions(positionsData));

    return positionsData;
  } catch (error) {
    return [];
  } finally {
    dispatch(setIsPositionsLoading(false));
  }
});

export const getMatchActivities = createAsyncThunk("match/getMatchActivities", async (_, { dispatch, getState }) => {
  const state = getState() as RootState;
  const services = state.auth.services;

  try {
    const response = await get(`/api/v1/match-activities?size=19`, services[FOOTBALL_SERVICE]);
    const activitiesData = response?.data?.data || [];
    dispatch(setMatchActivities(activitiesData));
    return activitiesData;
  } catch (error) {
    return [];
  }
});

export const submitMatchStats = createAsyncThunk(
  "match/submitMatchStats",
  async (
    requestBody: {
      matchId: string;
      opponentScoreTimes: number[];
      concludedTime: string;
      playerMatchActions: Array<{
        userId: string;
        sportsProfileId: string;
        position: string;
        jerseyNo: string;
        rating: string;
        playingTime: string;
        matchActivities: Array<{
          activityId: string;
          minutes: number[];
        }>;
      }>;
    },
    { getState }
  ) => {
    const state = getState() as RootState;
    const services = state.auth.services;

    try {
      const response = await post(`/api/v1/matches/${requestBody.matchId}/web-match-activities`, requestBody, services[EVENT_SERVICE]);

      return response;
    } catch (error) {
      throw error;
    }
  }
);

const downloadFileFromPresignedUrl = async (presignedUrl: string, filename: string) => {
  try {
    const link = document.createElement("a");
    link.href = presignedUrl;
    link.download = filename;
    link.target = "_blank";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    const response = await fetch(presignedUrl, {
      method: "GET",
      headers: {
        Accept: "*/*",
      },
    });

    if (response.ok) {
      const blob = await response.blob();

      if (blob.size > 0) {
        const url = window.URL.createObjectURL(blob);
        const blobLink = document.createElement("a");
        blobLink.href = url;
        blobLink.download = filename;
        document.body.appendChild(blobLink);
        blobLink.click();
        document.body.removeChild(blobLink);
        window.URL.revokeObjectURL(url);
      }
    }

    return { success: true, filename };
  } catch (error) {
    return { success: true, filename };
  }
};

export const downloadMatchReport = createAsyncThunk(
  "match/downloadMatchReport",
  async ({ teamId, matchId, teamName, opponentName }: { teamId: string; matchId: string; teamName?: string; opponentName?: string }, { getState }) => {
    const state = getState() as RootState;
    const services = state.auth.services;

    try {
      if (!services || !services[EVENT_SERVICE]) {
        throw new Error("Service configuration not available");
      }

      const params = new URLSearchParams({
        teamId,
        page: "1",
        size: "20",
        concludeStartDate: "1970-01-01T00:00:00.000Z",
        concludeEndDate: new Date().toISOString(),
      });

      const response = await get(`/api/v1/matches?${params.toString()}`, services[EVENT_SERVICE]);

      if (!response || !response.data || !response.data.data || !Array.isArray(response.data.data) || response.data.data.length === 0) {
        throw new Error("No match data found");
      }

      const matchData = response.data.data.find((match: any) => match._id === matchId);

      if (!matchData) {
        throw new Error("Match not found with the provided ID");
      }

      if (!matchData.reportUrl) {
        throw new Error("Report URL not available for this match");
      }

      const safeTeamName = (teamName || matchData.team?.name || "Team").replace(/[^a-zA-Z0-9]/g, "-");
      const safeOpponentName = (opponentName || matchData.opponent?.name || "Opponent").replace(/[^a-zA-Z0-9]/g, "-");
      const timestamp = new Date().toISOString().split("T")[0];
      const filename = `match-report-${safeTeamName}-vs-${safeOpponentName}-${timestamp}.pdf`;

      const result = await downloadFileFromPresignedUrl(matchData.reportUrl, filename);

      return { success: true, message: "Report downloaded successfully", filename: result.filename };
    } catch (error) {
      throw error;
    }
  }
);

export const getMatchStatsData = createAsyncThunk("match/getMatchStatsData", async ({ matchId }: { matchId: string }, { dispatch, getState }) => {
  const state = getState() as RootState;
  const services = state.auth.services;

  try {
    if (!services || !services[EVENT_SERVICE]) {
      throw new Error("Service configuration not available");
    }

    const response = await get(`/api/v1/matches/${matchId}/web-match-activities`, services[EVENT_SERVICE]);

    if (!response || !response.data) {
      return { opponentScoreTimes: [], playerMatchActions: [] };
    }

    const { opponentScoreTimes = [], playerMatchActions = [] } = response.data;

    if (opponentScoreTimes.length > 0) {
      const timeEntries = opponentScoreTimes.map((minute: number, index: number) => ({
        id: Date.now() + index,
        time: minute,
        type: "opponent",
      }));
      dispatch(setTimeEntries(timeEntries));
    }

    const currentPlayers = state.match.players;
    const matchActivities = state.match.matchActivities;

    if (playerMatchActions.length > 0 && currentPlayers.length > 0 && matchActivities.length > 0) {
      const activityMap: { [key: string]: string } = {};
      matchActivities.forEach((activity) => {
        activityMap[activity._id] = activity.code;
      });

      const updatedPlayers = currentPlayers.map((player) => {
        const playerData = playerMatchActions.find((action: any) => action.userId === player.userId || action.sportsProfileId === player.sportsProfileId);

        if (!playerData) return player;

        const updatedPlayer = {
          ...player,
          pos: playerData.position || player.pos,
          no: playerData.jerseyNo || player.no,
          ratings: playerData.rating || player.ratings,
          playingTime: playerData.playingTime || player.playingTime,
        };

        const goals: Array<{ id: number; minute: number }> = [];
        const assists: Array<{ id: number; minute: number }> = [];
        let redCard = "";
        let yellowCard = "";
        let subOut = "";
        let subIn = "";

        if (playerData.matchActivities) {
          playerData.matchActivities.forEach((activity: any) => {
            const activityCode = activityMap[activity.activityId];
            const minutes = activity.minutes.filter((m: number) => m > 0);

            if (activityCode === "GOAL_SCORED" && minutes.length > 0) {
              minutes.forEach((minute: number, index: number) => {
                goals.push({ id: Date.now() + index, minute });
              });
            } else if (activityCode === "ASSIST" && minutes.length > 0) {
              minutes.forEach((minute: number, index: number) => {
                assists.push({ id: Date.now() + index + 1000, minute });
              });
            } else if (activityCode === "RED_CARD" && minutes.length > 0) {
              redCard = minutes[0].toString();
            } else if (activityCode === "YELLOW_CARD" && minutes.length > 0) {
              yellowCard = minutes[0].toString();
            } else if (activityCode === "SUB_OUT" && minutes.length > 0) {
              subOut = minutes[0].toString();
            } else if (activityCode === "SUB_IN" && minutes.length > 0) {
              subIn = minutes[0].toString();
            }
          });
        }

        (updatedPlayer as any).goals = goals;
        (updatedPlayer as any).assists = assists;
        (updatedPlayer as any).red = redCard;
        (updatedPlayer as any).yellow = yellowCard;
        (updatedPlayer as any).subOut = subOut;
        (updatedPlayer as any).subIn = subIn;

        const playerHasStats = goals.length > 0 || assists.length > 0 || redCard || yellowCard || subIn || subOut || playerData.rating || playerData.playingTime;
        (updatedPlayer as any).hasStats = !!playerHasStats;

        return updatedPlayer;
      });

      // Sort players based on whether they have stats
      updatedPlayers.sort((a, b) => {
        const aHasStats =
          (Array.isArray(a.goals) && a.goals.length > 0) ||
          (Array.isArray((a as any).assists) && (a as any).assists.length > 0) ||
          !!a.red || !!a.yellow || !!a.subIn || !!a.subOut || !!a.ratings || !!a.playingTime;

        const bHasStats =
          (Array.isArray(b.goals) && b.goals.length > 0) ||
          (Array.isArray((b as any).assists) && (b as any).assists.length > 0) ||
          !!b.red || !!b.yellow || !!b.subIn || !!b.subOut || !!b.ratings || !!b.playingTime;

        if (aHasStats && !bHasStats) {
          return -1;
        }
        if (!aHasStats && bHasStats) {
          return 1; 
        }
        return 0;
      });

      dispatch(setPlayers(updatedPlayers));
    }

    return response.data;
  } catch (error) {
    return { opponentScoreTimes: [], playerMatchActions: [] };
  }
});

export const getTeamInfo = createAsyncThunk("match/getTeamInfo", async ({ teamId }: { teamId: string }, { dispatch, getState }) => {
  const state = getState() as RootState;
  const services = state.auth.services;

  try {
    const response = await get(`/api/v1/teams/${teamId}`, services[FOOTBALL_SERVICE]);
    const teamData = response?.data || null;

    if (teamData?.name) {
      dispatch(setTeamName(teamData.name));
      return teamData.name;
    }

    return "";
  } catch (error) {
    return "";
  }
});

export const downloadReportwithURL = createAsyncThunk("match/downloadReportWithUrl", async ({ reportUrl, reportName }: { reportUrl: string, reportName: string  }) => {
const safeReportName = (reportName).replace(/[^a-zA-Z0-9]/g, "-");
downloadFileFromPresignedUrl(reportUrl, safeReportName);
});

const MatchSlice = createSlice({
  name: "match",
  initialState,
  reducers: {
    setIsOpponentsLoading: (state, action) => {
      state.isOpponentsLoading = action.payload;
    },
    setIsPlayersLoading: (state, action) => {
      state.isPlayersLoading = action.payload;
    },
    setIsPositionsLoading: (state, action) => {
      state.isPositionsLoading = action.payload;
    },
    setOpponents: (state, action) => {
      state.opponents = action.payload.data;
    },
    setPlayers: (state, action) => {
      state.players = action.payload;
    },
    setPositions: (state, action) => {
      state.positions = action.payload;
    },
    setMatchActivities: (state, action) => {
      state.matchActivities = action.payload;
    },
    setSelectedOpponent: (state, action) => {
      state.selectedOpponent = action.payload;
    },
    setTimeEntries: (state, action) => {
      state.timeEntries = action.payload;
    },
    addTimeEntry: (state, action) => {
      state.timeEntries.push(action.payload);
    },
    removeTimeEntry: (state, action) => {
      state.timeEntries = state.timeEntries.filter((entry) => entry.id !== action.payload);
    },
    updatePlayerStats: (state, action) => {
      const { playerIndex, field, value } = action.payload;
      if (state.players[playerIndex]) {
        state.players[playerIndex] = {
          ...state.players[playerIndex],
          [field]: value,
        };
      }
    },
    setTeamName: (state, action) => {
      state.teamName = action.payload;
    },
    setMatchDuration: (state, action) => {
      state.matchDuration = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(downloadMatchReport.pending, (state) => {
        state.isDownloadingReport = true;
      })
      .addCase(downloadMatchReport.fulfilled, (state) => {
        state.isDownloadingReport = false;
      })
      .addCase(downloadMatchReport.rejected, (state) => {
        state.isDownloadingReport = false;
      })
      .addCase(getMatchStatsData.pending, (state) => {
        state.isLoadingMatchStats = true;
      })
      .addCase(getMatchStatsData.fulfilled, (state) => {
        state.isLoadingMatchStats = false;
      })
      .addCase(getMatchStatsData.rejected, (state) => {
        state.isLoadingMatchStats = false;
      });
  },
});

export const {
  setIsOpponentsLoading,
  setIsPlayersLoading,
  setIsPositionsLoading,
  setOpponents,
  setPlayers,
  setPositions,
  setMatchActivities,
  setSelectedOpponent,
  setTimeEntries,
  addTimeEntry,
  removeTimeEntry,
  updatePlayerStats,
  setTeamName,
  setMatchDuration,
} = MatchSlice.actions;

export default MatchSlice.reducer;
