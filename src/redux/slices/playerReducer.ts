import { createSlice } from '@reduxjs/toolkit';
import { createAsyncThunk } from '@reduxjs/toolkit';
import { post, get } from '@/api/amplifyApi';
import { RootState } from '@/redux/store';
import { saveSuccessCode } from '@/config/statusCodes';
import { FOOTBALL_SERVICE } from '@/api/apiTypes';

interface PlayerStat {
  sportsProfileId: string;
  current: number;
  target: number;
}

interface Player {
  firstName: string;
  lastName: string;
  profileImageUrl: string;
  isAvailable: boolean;
  sportsProfileId: string;
}

interface State {
  players: Player[];
  isPlayersLoading: boolean;
  playerStat: PlayerStat[];
  isStatLoading: boolean;
  isSaveButtonLoading: boolean;
  totalPlayers: number;
  currentPage: number;
  updatedPlayersStat: PlayerStat[];
  isSaveStatSuccess: boolean;
  hasSearchedByName: boolean;
  searchedName: string;
}

interface getStatParams {
  categoryId: string;
  criteriaId: string;
  sportsProfileIds: string[];
}

const initialState: State = {
  players: [],
  isPlayersLoading: true,
  playerStat: [],
  isStatLoading: false,
  isSaveButtonLoading: false,
  totalPlayers: 0,
  currentPage: 1,
  updatedPlayersStat: [],
  isSaveStatSuccess: false,
  hasSearchedByName: false,
  searchedName: '',
};

const PLAYER_COUNT = 20;

const PlayerSlice = createSlice({
  name: 'player',
  initialState,
  reducers: {
    setIsPlayersLoading: (state, action) => {
      state.isPlayersLoading = action.payload;
    },
    setPlayers: (state, action) => {
      state.players = action.payload;
    },
    setPlayerStat: (state, action) => {
      state.playerStat = action.payload;
    },
    setUpdatedPlayersStat: (state, action) => {
      state.updatedPlayersStat = action.payload;
    },
    setIsStatLoading: (state, action) => {
      state.isStatLoading = action.payload;
    },
    setIsSaveButtonLoading: (state, action) => {
      state.isSaveButtonLoading = action.payload;
    },
    setTotalPlayers: (state, action) => {
      state.totalPlayers = action.payload;
    },
    setCurrentPage: (state, action) => {
      state.currentPage = action.payload;
    },
    setHasSearchedByName: (state, action) => {
      state.hasSearchedByName = action.payload;
    },
    setSearchedName: (state, action) => {
      state.searchedName = action.payload;
    },
    setCurrentState: (state, action) => {
      const { sportsProfileId, value } = action.payload;

      const matchingUpdatedStat = state.updatedPlayersStat.find(
        (stat) => stat.sportsProfileId === sportsProfileId
      );

      const matchingStat = state.playerStat.find(
        (stat) => stat.sportsProfileId === sportsProfileId
      );

      if (matchingUpdatedStat) {
        matchingUpdatedStat.current += value;

        return state;
      } else {
        return {
          ...state,
          updatedPlayersStat: [
            ...state.updatedPlayersStat,
            {
              sportsProfileId,
              current: (matchingStat?.current ?? 0) + value,
              target: matchingStat?.target ?? 0,
            },
          ],
        };
      }
    },
    setTargetState: (state, action) => {
      const { sportsProfileId, value } = action.payload;

      const matchingUpdatedStat = state.updatedPlayersStat.find(
        (stat) => stat.sportsProfileId === sportsProfileId
      );

      const matchingStat = state.playerStat.find(
        (stat) => stat.sportsProfileId === sportsProfileId
      );

      if (matchingUpdatedStat) {
        matchingUpdatedStat.target += value;
      } else {
        return {
          ...state,
          updatedPlayersStat: [
            ...state.updatedPlayersStat,
            {
              sportsProfileId,
              current: matchingStat?.current ?? 0,
              target: matchingStat?.target + value,
            },
          ],
        };
      }
    },
  },
});

export const getPlayers = createAsyncThunk(
  'player/getPlayers',
  async (_, { dispatch, getState }) => {
    dispatch(setIsPlayersLoading(true));

    const teamId = localStorage.getItem('teamId');
    const state = getState() as RootState;
    const currentPage = state.player.currentPage;
    const isChart = state.chart.isChart;
    const services = state.auth.services;

    try {
      const players = await get(
        `/api/v1/teams/${teamId}/players?page=${currentPage}&size=${PLAYER_COUNT}`,
        services[FOOTBALL_SERVICE]
      );

      dispatch(setPlayers(players?.data?.data || []));
      dispatch(setTotalPlayers(players?.data?.totalRecords || 0));
      dispatch(setSearchedName(''));

      if (players?.data?.data && isChart) {
        dispatch(setIsStatLoading(true));
      }
    } catch (error) {
      console.log(error);
    } finally {
      dispatch(setIsPlayersLoading(false));
    }
  }
);

export const getPlayersByName = createAsyncThunk(
  'player/getPlayersByName',
  async (searchName: string, { dispatch, getState }) => {
    dispatch(setIsPlayersLoading(true));

    const teamId = localStorage.getItem('teamId');
    const state = getState() as RootState;
    const currentPage = state.player.currentPage;
    const services = state.auth.services;

    try {
      const players = await get(
        `/api/v1/teams/${teamId}/players?search=${searchName}` +
          `&page=${currentPage}&size=20`,
        services[FOOTBALL_SERVICE]
      );

      dispatch(setPlayers(players?.data?.data || []));
      dispatch(setTotalPlayers(players?.data?.totalRecords || 0));
      dispatch(setHasSearchedByName(true));
      dispatch(setSearchedName(searchName));

      if (players?.data?.data) {
        dispatch(setIsStatLoading(true));
      }
    } catch (error) {
      console.log(error);
    } finally {
      dispatch(setIsPlayersLoading(false));
    }
  }
);

export const getPlayerStat = createAsyncThunk(
  'player/getPlayerStat',
  async (
    { categoryId, criteriaId, sportsProfileIds }: getStatParams,
    { dispatch, getState }
  ) => {
    dispatch(setIsStatLoading(true));

    const state = getState() as RootState;
    const services = state.auth.services;
    const page = 1;
    const size = PLAYER_COUNT;

    try {
      const playerStat = await get(
        `/api/v1/iap-stats?sportsProfileIds=${sportsProfileIds.join(',')}` +
          `&categoryId=${categoryId}&criteriaId=${criteriaId}` +
          `&page=${page}&size=${size}`,
        services[FOOTBALL_SERVICE]
      );

      const playerStatData = playerStat?.data?.data || [];

      dispatch(setPlayerStat(playerStatData));
    } catch (error) {
      console.log(error);
    } finally {
      dispatch(setIsStatLoading(false));
    }
  }
);

export const savePlayerStat = createAsyncThunk(
  'player/savePlayerStat',
  async (_, { dispatch, getState }) => {
    dispatch(setIsSaveButtonLoading(true));

    const state = getState() as RootState;
    const updatedPlayersStat = state.player.updatedPlayersStat;
    const playersStat = state.player.playerStat;
    const categoryId = state.category.selectedCategoryId;
    const criteriaId = state.dashboard.selectedActivityId;
    const services = state.auth.services;

    const statData = {
      categoryId,
      criteriaId,
      playerCriteriaData: updatedPlayersStat,
    };
    try {
      const saveStatResponse = await post(
        `/api/v1/iap-stats/bulk`,
        statData,
        services[FOOTBALL_SERVICE]
      );

      if (saveStatResponse.status === saveSuccessCode) {
        const playerStatMap = new Map(
          playersStat.map((item) => [item.sportsProfileId, item])
        );
        updatedPlayersStat.forEach((updatedStat) => {
          const playerStatMatch = playerStatMap.get(
            updatedStat.sportsProfileId
          );
          if (playerStatMatch) {
            const updatedPlayerStatMatch = {
              ...playerStatMatch,
              current: updatedStat.current,
              target: updatedStat.target,
            };
            playerStatMap.set(
              updatedStat.sportsProfileId,
              updatedPlayerStatMatch
            );
          } else {
            const newStatsAddedPlayer = state.player.players?.find(
              (player) => player?.sportsProfileId == updatedStat.sportsProfileId
            );
            newStatsAddedPlayer &&
              playerStatMap.set(updatedStat.sportsProfileId, updatedStat);
          }
        });
        dispatch(setPlayerStat(Array.from(playerStatMap.values())));
        dispatch(setUpdatedPlayersStat([]));

        return true;
      } else {
        return false;
      }
    } catch (error) {
      console.log(error);
      return false;
    } finally {
      dispatch(setIsSaveButtonLoading(false));
    }
  }
);

export const {
  setIsPlayersLoading,
  setPlayers,
  setPlayerStat,
  setIsStatLoading,
  setCurrentState,
  setTargetState,
  setIsSaveButtonLoading,
  setTotalPlayers,
  setCurrentPage,
  setUpdatedPlayersStat,
  setHasSearchedByName,
  setSearchedName,
} = PlayerSlice.actions;

export default PlayerSlice.reducer;
