import { createSlice } from "@reduxjs/toolkit";
import { createAsyncThunk } from "@reduxjs/toolkit";
import { RootState } from "@/redux/store";
import { get } from "../../api/amplifyApi";
import { EVENT_SERVICE } from "@/api/apiTypes";

interface Team {
  _id: string;
  teamName: string;
}

interface Location {
  name : string
}

interface Tournament {
  name: string;
}

interface Event {
  _id: string;
  name: string;
  startTime: string;
  endTime: string;
  location: Location;
  tournament: Tournament;
  concluded : boolean;
  reportUrl : string;  
  submittedByWeb : boolean;

}

interface getEventParams {
  teamId: string;
  startDate: string;
  endDate: string;
}

interface EventState {
  events: Array<Event>;
  isEventLoading: boolean;
  selectedTeamFromDashbord : Team | null;
  selectedMonthFromDashBord : string;

}

const initialState: EventState = {
  events: [],
  isEventLoading: false,
  selectedTeamFromDashbord : null,
  selectedMonthFromDashBord : ''
};

const eventSlice = createSlice({
  initialState,
  name: "event",
  reducers: {
    setIsEventLoading: (state, action) => {
      state.isEventLoading = action.payload;
    },
    setEvents: (state, action) => {
      state.events = action.payload;
    },
    setSelectedTeamFromDashbord: (state, action) => {
      state.selectedTeamFromDashbord = action.payload;
    },
    setselectedMonthFromDashbord: (state, action) => {
      state.selectedMonthFromDashBord = action.payload;
    },
  },
});

export const getEvents = createAsyncThunk(
  "event/getEvents",
  async (
    { teamId, startDate , endDate } : getEventParams,
    { dispatch, getState }
  ) => {
    dispatch(setIsEventLoading(true));
    const state = getState() as RootState;
    const services = state.auth.services;
    const page = 1;
    const size = 100;
    try {
      let url = `/api/v1/web/matches?teamId=${teamId}&startDate=${startDate}&endDate=${endDate}&page=${page}&size=${size}`;
      const getEvents = await get(url, services[EVENT_SERVICE]);
      dispatch(setEvents(getEvents?.data?.data || []));
    } catch (error) {
      dispatch(setEvents([]))
      console.log(error);
    } finally {
      dispatch(setIsEventLoading(false));
    }
  }
);

export const { setIsEventLoading, setEvents, setSelectedTeamFromDashbord, setselectedMonthFromDashbord } = eventSlice.actions;

export default eventSlice.reducer;
