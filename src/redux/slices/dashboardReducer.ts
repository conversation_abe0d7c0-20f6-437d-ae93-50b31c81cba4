import { createSlice } from '@reduxjs/toolkit';
import { createAsyncThunk } from '@reduxjs/toolkit';
import { RootState } from '@/redux/store';
import { get } from '@/api/amplifyApi';
import { FOOTBALL_SERVICE } from '@/api/apiTypes';

interface Level {
  start: number,
  end: number,
  colorCode: string
}

interface Activity {
  _id: string;
  activity: string;
  maximum: number,
  minimum: number,
  levels: Level[],
}

interface DashboardState {
  activities: Activity[];
  isActivitiesLoading: boolean;
  selectedActivityId: string;
}

const initialState: DashboardState = {
  activities: [],
  isActivitiesLoading: true,
  selectedActivityId: '',
};

const DashboardSlice = createSlice({
  name: 'dashboard',
  initialState,
  reducers: {
    setIsActivitiesLoading: (state, action) => {
      state.isActivitiesLoading = action.payload;
    },
    setActivities: (state, action) => {
      state.activities = action.payload;
    },
    setSelectedActivityId: (state, action) => {
      state.selectedActivityId = action.payload;
    },
  },
});

export const getActivities = createAsyncThunk(
  'dashboard/getActivities',
  async ({ categoryId }: { categoryId: string }, { dispatch, getState }) => {
    dispatch(setIsActivitiesLoading(true));

    const state = getState() as RootState;
    const services = state.auth.services;

    try {
      const activities = await get(
        `/api/v1/iap-categories/${categoryId}/criterion`,
        services[FOOTBALL_SERVICE]
      );

      const activitiesData = activities?.data || [];

      dispatch(setActivities(activitiesData));

      const firstCriteriaId = activitiesData[0]?._id?.toLowerCase();

      if (firstCriteriaId) {
        dispatch(setSelectedActivityId(firstCriteriaId));
      }
    } catch (error) {
      console.log(error);
    } finally {
      dispatch(setIsActivitiesLoading(false));
    }
  }
);

export const { setIsActivitiesLoading, setActivities, setSelectedActivityId } =
  DashboardSlice.actions;

export default DashboardSlice.reducer;
