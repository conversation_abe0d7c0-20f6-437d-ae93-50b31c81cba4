import { createSlice } from '@reduxjs/toolkit';
import { createAsyncThunk, createSelector } from '@reduxjs/toolkit';
import { get } from '@/api/amplifyApi';
import { RootState } from '@/redux/store';
import { FOOTBALL_SERVICE } from '@/api/apiTypes';

interface Catergory {
  name: string;
  _id: string;
}

interface CategoryState {
  categories: Catergory[];
  isCategoryLoading: boolean;
  selectedCategoryId: string;
}

const initialState: CategoryState = {
  categories: [],
  isCategoryLoading: true,
  selectedCategoryId: '',
};

const CategorySlice = createSlice({
  name: 'category',
  initialState,
  reducers: {
    setIsCategoryLoading: (state, action) => {
      state.isCategoryLoading = action.payload;
    },
    setCategories: (state, action) => {
      state.categories = action.payload;
    },
    setSelectedCategoryId: (state, action) => {
      state.selectedCategoryId = action.payload;
    },
  },
});

export const getCategories = createAsyncThunk(
  'category/getCategories',
  async (_, { dispatch, getState }) => {
    dispatch(setIsCategoryLoading(true));

    const state = getState() as RootState;
    const services = state.auth.services;

    try {
      const categories = await get(
        `/api/v1/iap-categories`,
        services[FOOTBALL_SERVICE]
      );

      dispatch(setCategories(categories?.data || []));
    } catch (error) {
      console.log(error);
    } finally {
      dispatch(setIsCategoryLoading(false));
    }
  }
);

export const selectCategoryIdByName = (name: string) =>
  createSelector(
    (state: RootState) => state?.category.categories,
    (categories) => {
      const category = categories.find((c) => c.name.toLowerCase() === name);
      return category ? category._id : '';
    }
  );

export const { setIsCategoryLoading, setCategories, setSelectedCategoryId } =
  CategorySlice.actions;

export default CategorySlice.reducer;
