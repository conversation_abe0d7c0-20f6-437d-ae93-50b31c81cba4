import { createSlice } from '@reduxjs/toolkit';
import { createAsyncThunk } from '@reduxjs/toolkit';
import { get } from '@/api/amplifyApi';
import { RootState } from '@/redux/store';
import { FOOTBALL_SERVICE } from '@/api/apiTypes';

interface ChartData {
  current: number;
  target: number;
  dateTime: string;
}

interface ChartState {
  chartData: ChartData[];
  isChartLoading: boolean;
  slectedPlayerId: string;
  isChart: boolean;
  dateRange: {
    fromDate: string;
    toDate: string;
  };
}

const initialState: ChartState = {
  chartData: [],
  isChartLoading: false,
  slectedPlayerId: '',
  isChart: false,
  dateRange: {
    fromDate: '',
    toDate: '',
  },
};

const chartSlice = createSlice({
  name: 'chart',
  initialState,
  reducers: {
    setIsChartLoading: (state, action) => {
      state.isChartLoading = action.payload;
    },
    setChartData: (state, action) => {
      state.chartData = action.payload;
    },
    setSlectedPlayerId: (state, action) => {
      state.slectedPlayerId = action.payload;
    },
    setIsChart: (state, action) => {
      state.isChart = action.payload;
    },
    setDateRange: (state, action) => {
      state.dateRange = action.payload;
    },
  },
});

export const getChartData = createAsyncThunk(
  'chart/getChartData',
  async (_, { dispatch, getState }) => {
    dispatch(setIsChartLoading(true));

    const state = getState() as RootState;
    const playerId = state.chart.slectedPlayerId;
    const fromDate = state.chart.dateRange.fromDate;
    const toDate = state.chart.dateRange.toDate;
    const categoryId = state.category.selectedCategoryId;
    const criteriaId = state.dashboard.selectedActivityId;
    const services = state.auth.services;
    const page = 1;
    const size = 1000;

    try {
      const chartData = await get(
        `/api/v1/sport-profiles/${playerId}/categories/${categoryId}/criteria/` +
          `${criteriaId}/iap-stats?startDate=${fromDate}&endDate=${toDate}` +
          `&page=${page}&size=${size}`,
        services[FOOTBALL_SERVICE]
      );

      dispatch(setChartData(chartData?.data?.data || []));
    } catch (error) {
      console.log(error);
    } finally {
      dispatch(setIsChartLoading(false));
    }
  }
);

export const {
  setIsChartLoading,
  setChartData,
  setSlectedPlayerId,
  setIsChart,
  setDateRange,
} = chartSlice.actions;

export default chartSlice.reducer;
