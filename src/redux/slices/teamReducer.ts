import { createSlice } from '@reduxjs/toolkit';
import { createAsyncThunk } from '@reduxjs/toolkit';
import { RootState } from '@/redux/store';
import { get } from '../../api/amplifyApi';
import { FOOTBALL_SERVICE } from '@/api/apiTypes';

interface Team {
  _id: string;
  teamName: string;
}

interface TeamsState {
  teams: Array<Team>;
  isTeamLoading: boolean;
  isPageLoading: boolean;
}

const initialState: TeamsState = {
  teams: [],
  isTeamLoading: false,
  isPageLoading: true,
};

const teamSlice = createSlice({
  initialState,
  name: 'team',
  reducers: {
    setIsTeamLoading: (state, action) => {
      state.isTeamLoading = action.payload;
    },
    setTeams: (state, action) => {
      state.teams = action.payload;
    },
    setIsPageLoading: (state, action) => {
      state.isPageLoading = action.payload;
    },
  },
});

export const getTeams = createAsyncThunk(
  'team/getTeams',
  async (payload: { searchKey?: string } | void, { dispatch, getState }) => {
    dispatch(setIsTeamLoading(true));

    const coachId = localStorage.getItem('coachId');
    const state = getState() as RootState;
    const services = state.auth.services;
    const page = 1;
    const size = 100;
    const { searchKey } = payload || {};

    try {
      let url = `/api/v1/teams?coachId=${coachId}&page=${page}&size=${size}`;
      if (searchKey && searchKey.trim()) {
        url += `&searchTeamNameLike=${searchKey}`;
      }
      const getTeams = await get(url,
        services[FOOTBALL_SERVICE]
      );

      dispatch(setTeams(getTeams?.data?.data || []));
    } catch (error) {
      console.log(error);
    } finally {
      dispatch(setIsTeamLoading(false));
    }
  }
);

export const { setIsTeamLoading, setTeams, setIsPageLoading } =
  teamSlice.actions;

export default teamSlice.reducer;
