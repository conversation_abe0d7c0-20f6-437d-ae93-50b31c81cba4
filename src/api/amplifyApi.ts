import { API, Auth } from 'aws-amplify';
import { LOGGED, NOTLOGGED } from '@/const/loginState';

const apiName = 'execute-api';

export const get = async (url: string, serviceType: string) => {
  try {
    const session = await Auth?.currentSession();
    const accessToken = session?.getAccessToken()?.getJwtToken();
    const path = `/${serviceType}${url}`;
    const config = {
      headers: {
        accessToken: accessToken || '',
      },
      response: true,
      timeout: 120000,
    };

    return API.get(apiName, path, config);
  } catch (error) {
    console.log(error);
  }
};

export const post = async (url: string, data: any, serviceType: string) => {
  try {
    const session = await Auth?.currentSession();
    const accessToken = session?.getAccessToken()?.getJwtToken();
    const path = `/${serviceType}${url}`;
    const config = {
      headers: {
        'Content-Type': 'application/json',
        accessToken: accessToken || '',
      },
      timeout: 120000,
      response: true,
      body: data,
    };

    return API.post(apiName, path, config);
  } catch (error) {
    console.log(error);
  }
};

export const put = async (url: string, data: any, serviceType: string) => {
  try {
    const session = await Auth?.currentSession();
    const accessToken = session?.getAccessToken()?.getJwtToken();
    const path = `/${serviceType}${url}`;
    const config = {
      headers: {
        accessToken: accessToken || '',
      },
      timeout: 120000,
      response: true,
      body: data,
    };

    return API.put(apiName, path, config);
  } catch (error) {
    console.log(error);
  }
};

export const deleteRequest = (url: string, serviceType: string) => {
  const path = `/${serviceType}${url}`;
  const config = {
    headers: {},
    response: true,
    timeout: 120000,
  };

  return API.del(apiName, path, config);
};

export const getLoginState = async () => {
  try {
    const session = await Auth?.currentSession();
    const accessToken = session?.getAccessToken()?.getJwtToken();

    return accessToken ? LOGGED : NOTLOGGED;
  } catch (error) {
    return NOTLOGGED;
  }
};
