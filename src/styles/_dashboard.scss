$small: 480px;

.dashboard-body {
  background-color: var(--knod-dark-green);

  .top-navbar-dashboard {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: none;
    color: var(--text-color);
    height: 87px;
    padding: 0px 4px;

    @media screen and (max-width: $small) {
      height: 60px;
    }

    .user-profile {
      @media screen and (max-width: $small) {
        display: none;
      }
    }
  }

  .team-name {
    text-align: center;
    font-size: 18px;
    font-family: 'poppinsmedium', Arial, Helvetica, sans-serif;
  }

  .back-btn {
    color: var(--text-color);
    background: none;
    border: none;
  }

  @media screen and (max-width: $small) {
    .team-name {
      position: absolute;
      left: 50%;
      top: 26.8px;
      width: 200px;
      -ms-transform: translate(-50%, -50%);
      transform: translate(-50%, -50%);
      font-size: 16px;
    }
  }

  .activity-container {
    overflow-x: auto;
  }

  .button-container {
    display: flex;
    justify-content: space-around;
    align-items: center;
    font-family: 'poppinsmedium', Arial, Helvetica, sans-serif;

    @media screen and (max-width: $small) {
      font-size: 13px;
      width: 600px;
      display: flex;
      column-gap: 2px;
    }

    .tab-btn {
      background: none;
      color: var(--text-color);
      border: none;
      text-align: center;
      padding: 0;
      font-size: 15px/67px;
      font-weight: normal;

      @media screen and (max-width: $small) {
        font-size: 13px;
        white-space: nowrap;
        margin: 0 5px;
      }
    }

    /* Style when the button is hovered over */
    .tab-btn:hover {
      opacity: 0.7;
    }

    /* Style when the button is active (clicked) */
    .tab-btn:active {
      border-bottom: 3px solid var(--knod-light-green);
    }

    .active {
      border-bottom: 3px solid var(--knod-light-green);
    }
  }

  .horizontal-line {
    margin-top: 10px;
    background-color: var(--search-background-color);
    height: 1px;
    min-width: 100%;
    margin-right: calc(var(--bs-gutter-x) * -0.5);
    margin-left: calc(var(--bs-gutter-x) * -0.5);
  }

  .chart {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 300px;
    width: 100%;

    .graph-container {
      width: 100%;
      height: 100%;
      padding-top: 20px;
      padding-bottom: 18px;

      .date-range {
        color: var(--text-color);
        text-align: center;
        font-size: 15px;
      }
    }

    h3 {
      color: var(--search-background-color);
    }

    .echart {
      height: 600px !important;
      max-width: 99.9%;

      @media screen and (max-width: $small) {
        height: 450px !important;
      }
    }
  }
}

.alert-modal {
  .modal-content {
    background-color: var(--knod-dark-green);
    color: var(--text-color);
  }

  .modal-header {
    border-color: var(--search-background-color) !important;
  }

  .modal-footer {
    border-color: var(--search-background-color) !important;
  }

  .yes-btn {
    background-color: var(--knod-light-green) !important;
    border-color: var(--knod-light-green) !important;
  }

  button {
    font-weight: bold;
  }
}

.input-container {
  position: relative;

  .date-input-container {
    position: relative;
    display: flex;

    input[type='date']::-webkit-calendar-picker-indicator {
      z-index: 1;
    }

    .icon {
      position: absolute;
      right: 16px;
      top: 56%;
      transform: translateY(-50%);
      cursor: pointer;
      color: var(--text-color);
      font-size: 13px;
    }
  }
}
