$medium: 720px;
$large: 1200px;
$box-shadow-color: #00000029;

.home {
  background-color: var(--knod-dark-green);
  min-height: 100vh;

  .team-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: var(--text-color);
    margin-left: auto;
    margin-right: auto;

    @media screen and (min-width: $large) {
      max-width: 70vw;
    }

    @media screen and (min-width: $medium) {
      max-width: 80vw;
    }
  }

  .logo {
    max-width: 150px;
    margin-top: 20px;
  }

  .title-label {
    text-align: center;
    font-size: 35px/44px;
    line-height: 43px;
    font-family: Futura;
    font-weight: 700;
    letter-spacing: 0px;
    opacity: 1;
    font-family: 'futura_stdheavy', Arial, Helvetica, sans-serif;
  }

  .title-label-2 {
    text-align: center;
    font-size: 19px/29px;
    line-height: 43px;
    font-family: Futura;
    font-weight: 700;
    letter-spacing: 0px;
    text-transform: uppercase;
    opacity: 1;
    color: #00ff6f;
    font-family: 'poppinsmedium', Arial, Helvetica, sans-serif;
  }

  .team-card {
    background: var(--navbar-color) 0% 0% no-repeat padding-box;
    box-shadow: 0px 3px 11px $box-shadow-color;
    border-radius: 16px;
    opacity: 1;
    padding: 0px 15px;
    margin-top: 10px;
    height: 115px;
    text-align: center;
    display: flex;
    align-items: center;
    cursor: pointer;
    text-decoration: none;
    color: var(--text-color);
    text-transform: uppercase;

    &:hover {
      background-color: var(--knod-green-hover);
    }
  }

  .card-title {
    font-size: 14px;
    font-family: 'poppinsmedium', Arial, Helvetica, sans-serif;
  }

  .navBar {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    background-color: #0E1B2E;
  }

  .mainContent {
    min-height: 90vh;
    background-color: #0E1B2E;
    display: flex;
    align-items: center ;
    justify-content: center;

    .container {
      max-width: 1200px;
      width: 100%;
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 80px;
      align-items: center;
    }

    .main-title {
      font-size: 96px;
      font-weight: bold;
      margin-bottom: 40px;
      letter-spacing: 4px;
      color: white;
      line-height: 1;
    }

    .second-title {
      font-size: 18px;
      font-weight: normal;
      margin-bottom: 20px;
      color: white;
    }

    .rightContent {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 30px;
    }

    .tile {
      background-color: rgba(71, 85, 105, 0.4);
      border-radius: 24px;
      padding: 60px 30px;
      display: flex;
      align-items: center;
      justify-content: center;
      aspect-ratio: 1;
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .tileText {
      color: white;
      font-size: 24px;
      font-weight: 600;
      letter-spacing: 1px;
      text-align: center;
    }
  }
}
