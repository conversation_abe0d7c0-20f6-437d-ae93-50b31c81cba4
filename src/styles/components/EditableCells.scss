@import "../variables.scss";

// Editable Input Components Styles

.editable-cell {
  &__input {
    width: 100%;
    border: 1px solid $border-light;
    border-radius: $border-radius-sm;
    padding: $spacing-xs $spacing-sm;
    font-size: $font-size-sm;
    text-align: center;
    background-color: transparent;
    transition: all $transition-fast;

    &:focus {
      outline: none;
      border-color: $primary-green;
      box-shadow: $focus-shadow;
      background-color: rgba(255, 255, 255, 0.8);
    }

    &:hover:not(:focus) {
      border-color: #b0b0b0;
    }

    &[type="number"] {
      appearance: textfield;
      -moz-appearance: textfield;

      &::-webkit-outer-spin-button,
      &::-webkit-inner-spin-button {
        -webkit-appearance: none;
        margin: 0;
      }
    }

    &--invalid {
      background-color: rgba(255, 0, 0, 0.1);
      border-color: #ff4444;

      &:focus {
        background-color: rgba(255, 0, 0, 0.15);
        border-color: #ff4444;
        box-shadow: 0 0 0 2px rgba(255, 68, 68, 0.2);
      }
    }
  }

  &__display {
    font-size: $font-size-sm;
    color: $text-dark;
    display: inline-block;
    min-height: 20px;
    line-height: 20px;
  }
}

.goals-cell {
  &__input {
    width: 100%;
    border: 1px solid $border-light;
    border-radius: $border-radius-sm;
    padding: $spacing-xs $spacing-sm;
    font-size: $font-size-sm;
    text-align: center;
    background-color: transparent;
    transition: all $transition-fast;

    &:focus {
      outline: none;
      border-color: $primary-green;
      box-shadow: $focus-shadow;
      background-color: rgba(255, 255, 255, 0.8);
    }

    &:hover:not(:focus) {
      border-color: #b0b0b0;
    }

    &::placeholder {
      color: $text-light;
      font-style: italic;
      font-size: $font-size-xs;
    }

    &--invalid {
      background-color: rgba(255, 0, 0, 0.1);
      border-color: #ff4444;

      &:focus {
        background-color: rgba(255, 0, 0, 0.15);
        border-color: #ff4444;
        box-shadow: 0 0 0 2px rgba(255, 68, 68, 0.2);
      }
    }
  }

  &__display {
    font-size: $font-size-sm;
    color: $text-dark;
    display: inline-block;
    min-height: 20px;
    line-height: 20px;
  }
}

.position-dropdown {
  &__select {
    width: 100%;
    border: 1px solid $border-light;
    border-radius: $border-radius-sm;
    padding: $spacing-xs $spacing-sm;
    font-size: $font-size-sm;
    background-color: transparent;
    cursor: pointer;
    transition: all $transition-fast;

    &:focus {
      outline: none;
      border-color: $primary-green;
      box-shadow: $focus-shadow;
    }

    &:hover:not(:focus) {
      border-color: #b0b0b0;
    }

    option {
      padding: $spacing-xs;
      background-color: $background-white;
    }
  }

  &__display {
    font-size: $font-size-sm;
    color: $text-medium;
    display: inline-block;
    min-height: 20px;
    line-height: 20px;
  }
}
