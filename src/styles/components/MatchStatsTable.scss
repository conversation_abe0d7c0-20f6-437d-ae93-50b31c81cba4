@import "../variables.scss";

// Match Stats Table Styles

.match-stats-container {
  min-height: 100vh;
  background-color: $background-light;
  padding: $spacing-xl;
}

.match-header {
  background-color: $background-white;
  padding: $spacing-lg $spacing-xxl;
  border-radius: $border-radius-lg;
  margin-bottom: $spacing-xl;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: $shadow-sm;

  &__logo-section {
    display: flex;
    align-items: center;
    gap: $spacing-md;

    .logo-icon {
      width: 40px;
      height: 40px;
      background-color: $primary-green;
      border-radius: $border-radius-md;
      display: flex;
      align-items: center;
      justify-content: center;
      color: $background-white;
      font-weight: $font-weight-bold;
      font-size: $font-size-md;
    }

    .logo-image {
      height: auto;
      max-height: 75px;
      width: auto;
      object-fit: contain;
    }
  }

  &__score-section {
    display: flex;
    align-items: center;
    gap: $spacing-xl;

    .team-name {
      font-size: $font-size-lg;
      font-weight: $font-weight-bold;
      color: $text-dark;
    }

    .score-home {
      background-color: $primary-green;
      color: $background-white;
      padding: $spacing-md $spacing-xl;
      border-radius: $border-radius-lg;
      font-size: $font-size-xxl;
      font-weight: $font-weight-bold;
      min-width: 60px;
      text-align: center;
    }

    .vs-text {
      font-size: $font-size-xxl;
      font-weight: $font-weight-bold;
      color: $text-dark;
    }

    .score-away {
      background-color: $danger-red;
      color: $background-white;
      padding: $spacing-md $spacing-xl;
      border-radius: $border-radius-lg;
      font-size: $font-size-xxl;
      font-weight: $font-weight-bold;
      min-width: 60px;
      text-align: center;
    }

    .opponent-name {
      font-size: $font-size-lg;
      font-weight: $font-weight-bold;
      color: $text-dark;
    }
  }

  &__submit-btn {
    background-color: $primary-green;
    color: $background-white;
    padding: $spacing-md $spacing-xxl;
    border-radius: $border-radius-pill;
    border: none;
    font-size: $font-size-md;
    font-weight: $font-weight-semibold;
    cursor: pointer;
    transition: background-color $transition-fast;

    &:hover:not(:disabled) {
      background-color: $primary-green-hover;
    }

    &:disabled {
      background-color: $gray-400;
      cursor: not-allowed;
      opacity: 0.6;
    }
  }

  &__download-btn {
    background-color: $primary-green;
    color: $background-white;
    padding: $spacing-md $spacing-xxl;
    border-radius: $border-radius-pill;
    border: none;
    font-size: $font-size-md;
    font-weight: $font-weight-semibold;
    cursor: pointer;
    transition: all $transition-fast;
    display: flex;
    align-items: center;
    gap: $spacing-sm;

    &:hover:not(:disabled) {
      background-color: $primary-green-hover;
    }

    &:disabled {
      background-color: $border-light;
      color: $text-medium;
      cursor: not-allowed;
      opacity: 0.6;

      &:hover {
        background-color: $border-light;
      }
    }
  }

  &__submitted-status {
    display: flex;
    align-items: center;
    gap: $spacing-sm;
    color: $success-green;
    font-size: $font-size-md;
    font-weight: $font-weight-semibold;
    padding: $spacing-md $spacing-lg;
    background-color: rgba($success-green, 0.1);
    border-radius: $border-radius-lg;
  }
}

.controls-section {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: $spacing-xl;

  &__left {
    display: flex;
    align-items: center;
    gap: $spacing-xl;
  }

  .back-btn {
    display: flex;
    align-items: center;
    gap: $spacing-sm;
    background-color: transparent;
    border: none;
    font-size: $font-size-md;
    color: $text-dark;
    cursor: pointer;
    font-weight: $font-weight-medium;
    transition: color $transition-fast;

    &:hover {
      color: $primary-green;
    }
  }

  .match-duration-input {
    display: flex;
    align-items: center;
    gap: $spacing-lg;

    &__label {
      font-size: $font-size-sm;
      color: $text-medium;
      font-weight: $font-weight-medium;
    }

    &__container {
      display: flex;
      align-items: center;
      gap: $spacing-xs;
      background-color: $background-white;
      padding: 6px $spacing-md;
      border-radius: $border-radius-xl;
      border: 1px solid $border-light;

      &--invalid {
        background-color: rgba(255, 0, 0, 0.1);
        border-color: #ff4444;

        &:focus-within {
          background-color: rgba(255, 0, 0, 0.15);
          border-color: #ff4444;
          box-shadow: 0 0 0 2px rgba(255, 68, 68, 0.2);
        }
      }
    }

    &__field {
      width: 40px;
      border: none;
      outline: none;
      font-size: $font-size-sm;
      text-align: center;
      background-color: transparent;
      border-radius: 3px;

      &:disabled {
        color: $text-light;
        cursor: not-allowed;
      }

      &::placeholder {
        color: $text-light;
        font-size: $font-size-sm;
      }
    }

    &__unit {
      font-size: $font-size-sm;
      color: $text-dark;
    }

    &__error {
      color: $danger-red;
      font-size: $font-size-xs;
      font-weight: $font-weight-medium;
      position: absolute;
      top: 100%;
      left: 0;
      margin-top: $spacing-xs;
      white-space: nowrap;
    }

    &__required {
      color: $warning-yellow;
      font-size: $font-size-xs;
      font-weight: $font-weight-medium;
      position: absolute;
      top: 100%;
      left: 0;
      margin-top: $spacing-xs;
      white-space: nowrap;
    }
  }

  .opponent-score {
    display: flex;
    align-items: center;
    gap: $spacing-lg;

    &__label {
      font-size: $font-size-sm;
      color: $text-medium;
      font-weight: $font-weight-medium;
    }

    &__entries {
      display: flex;
      gap: $spacing-sm;
    }

    &__entry {
      display: flex;
      align-items: center;
      gap: $spacing-xs;
      background-color: $background-white;
      padding: 6px $spacing-md;
      border-radius: $border-radius-xl;
      border: 1px solid $border-light;

      .time-text {
        font-size: $font-size-sm;
        color: $text-dark;
      }

      .remove-btn {
        width: $spacing-xl;
        height: $spacing-xl;
        background-color: $danger-red;
        border-radius: $border-radius-circle;
        display: flex;
        align-items: center;
        justify-content: center;
        color: $background-white;
        font-size: $font-size-xs;
        font-weight: $font-weight-bold;
        cursor: pointer;
        transition: background-color $transition-fast;

        &:hover {
          background-color: $danger-red-hover;
        }
      }
    }

    &__add-section {
      display: flex;
      align-items: center;
      gap: $spacing-xs;
      background-color: $background-white;
      padding: 6px $spacing-md;
      border-radius: $border-radius-xl;
      border: 1px solid $border-light;

      &--invalid {
        background-color: rgba(255, 0, 0, 0.1);
        border-color: #ff4444;

        &:focus-within {
          background-color: rgba(255, 0, 0, 0.15);
          border-color: #ff4444;
          box-shadow: 0 0 0 2px rgba(255, 68, 68, 0.2);
        }
      }

      .time-input {
        width: 40px;
        border: none;
        outline: none;
        font-size: $font-size-sm;
        text-align: right;
        margin-right: 10px;
        background-color: transparent;
        border-radius: 3px;
      }

      &--invalid .time-input {
        background-color: rgba(255, 0, 0, 0.2);
        border: 1px solid #ff4444;

        &:focus {
          background-color: rgba(255, 0, 0, 0.25);
          border-color: #ff4444;
          outline: none;
        }
      }

      .add-btn {
        width: $spacing-xl;
        height: $spacing-xl;
        background-color: $primary-green;
        border-radius: $border-radius-circle;
        display: flex;
        align-items: center;
        justify-content: center;
        color: $background-white;
        font-size: $font-size-xs;
        font-weight: $font-weight-bold;
        cursor: pointer;
        transition: background-color $transition-fast;

        &:hover {
          background-color: $primary-green-hover;
        }

        &--disabled {
          background-color: $text-light;
          cursor: not-allowed;
          opacity: 0.6;

          &:hover {
            background-color: $text-light;
          }
        }
      }
    }
  }
}

.stats-table {
  background-color: $background-white;
  border-radius: $border-radius-lg;
  overflow: hidden;
  box-shadow: $shadow-sm;

  table {
    width: 100%;
    border-collapse: collapse;
  }

  th:first-child,
  td:first-child {
    width: 80px;
    min-width: 80px;
    max-width: 80px;
  }

  th:last-child,
  td:last-child {
    width: 120px;
    min-width: 120px;
    max-width: 120px;
  }

  &__header {
    background-color: $background-gray;

    th {
      padding: $spacing-lg $spacing-md;
      font-size: $font-size-sm;
      font-weight: $font-weight-semibold;
      color: $text-dark;
      border-bottom: 1px solid $border-light;
      position: relative;

      &.text-left {
        text-align: left;
      }

      &.text-center {
        text-align: center;
      }

      &:hover {
        background-color: #eeeeee;
        transition: background-color $transition-fast;
      }

      .header-with-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: $spacing-xs;

        img {
          object-fit: contain;
          filter: grayscale(20%);
        }

        span {
          font-size: $font-size-xs;
          font-weight: $font-weight-semibold;
        }
      }

      .name-header-with-search {
        display: flex;
        align-items: center;
        width: 100%;
        min-height: 24px;

        .name-header-top {
          display: flex;
          align-items: center;
          justify-content: space-between;
          width: 100%;
          gap: $spacing-sm;

          span {
            font-size: $font-size-sm;
            font-weight: $font-weight-semibold;
          }

          .search-icon {
            cursor: pointer;
            opacity: 0.6;
            transition: all 0.2s ease;
            color: $text-medium;

            &:hover {
              opacity: 1;
              color: $primary-green;
            }
          }
        }

        .player-search-input {
          width: 100%;
          padding: $spacing-xs $spacing-sm;
          border: 1px solid $border-light;
          border-radius: $border-radius-sm;
          font-size: $font-size-sm;
          background-color: $background-white;
          transition: all 0.2s ease;
          font-weight: $font-weight-semibold;

          &:focus {
            outline: none;
            border-color: $primary-green;
            box-shadow: 0 0 0 2px rgba($primary-green, 0.1);
          }

          &::placeholder {
            color: $text-light;
            font-size: $font-size-sm;
          }
        }
      }
    }
  }

  &__body {
    tr {
      border-bottom: 1px solid $border-lighter;
      transition: background-color $transition-fast;

      &:hover {
        background-color: $primary-green-light;
      }

      &:last-child {
        border-bottom: none;
      }

      &.has-stats {
        background-color: #f0fff9;
        &:hover {
          background-color: #e6ffee;
        }
      }
    }

    td {
      padding: $spacing-md;
      font-size: $font-size-sm;
      color: $text-dark;
      position: relative;

      &.text-center {
        text-align: center;
      }

      &.player-name {
        font-weight: $font-weight-medium;
        color: $text-dark;
      }

      &.rating {
        font-weight: $font-weight-semibold;

        .editable-cell__input,
        .editable-cell__display {
          font-weight: $font-weight-semibold;
        }
      }

      &.position {
        color: $text-medium;
      }
    }
  }

  &--disabled {
    position: relative;
    pointer-events: none;
    opacity: 0.6;
  }

  &__disabled-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba($background-white, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
    border-radius: $border-radius-lg;
  }

  &__disabled-message {
    background-color: $warning-yellow;
    color: $background-white;
    padding: $spacing-md $spacing-lg;
    border-radius: $border-radius-md;
    font-size: $font-size-md;
    font-weight: $font-weight-semibold;
    text-align: center;
    box-shadow: $shadow-md;
  }
}

// Loading and Error States
.loading-container,
.error-container {
  min-height: 100vh;
  background-color: $background-light;
  padding: $spacing-xl;
  display: flex;
  align-items: center;
  justify-content: center;

  .message {
    font-size: $font-size-lg;
    color: $text-medium;
    text-align: center;
  }
}

// No Data State
.no-data-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
  background-color: $background-white;
  border-radius: $border-radius-lg;
  margin: $spacing-xl 0;
  padding: $spacing-xxl;
  box-shadow: $shadow-sm;

  .no-data-message {
    text-align: center;
    max-width: 400px;

    h3 {
      font-size: $font-size-xl;
      color: $text-dark;
      font-weight: $font-weight-bold;
      margin-bottom: $spacing-md;
    }

    p {
      font-size: $font-size-md;
      color: $text-medium;
      line-height: 1.5;
      margin: 0;
    }
  }
}

// Dialog Styles
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.dialog-box {
  background-color: $background-white;
  border-radius: $border-radius-lg;
  padding: $spacing-xxl;
  max-width: 400px;
  width: 90%;
  text-align: center;
  box-shadow: $shadow-md;

  .dialog-icon {
    width: 60px;
    height: 60px;
    border-radius: $border-radius-circle;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto $spacing-lg;
    font-size: $font-size-xxl;
    font-weight: $font-weight-bold;
    color: $background-white;

    &.warning {
      background-color: $warning-yellow;
    }

    &.error {
      background-color: $danger-red;
    }

    &.success {
      background-color: $success-green;
    }
  }

  h3 {
    font-size: $font-size-lg;
    font-weight: $font-weight-bold;
    color: $text-dark;
    margin-bottom: $spacing-md;
  }

  p {
    font-size: $font-size-sm;
    color: $text-medium;
    margin-bottom: $spacing-xl;
    line-height: 1.4;
  }

  .dialog-buttons {
    display: flex;
    gap: $spacing-md;
    justify-content: center;
  }

  .dialog-btn {
    padding: $spacing-md $spacing-xl;
    border: none;
    border-radius: $border-radius-sm;
    font-size: $font-size-sm;
    font-weight: $font-weight-semibold;
    cursor: pointer;
    transition: background-color $transition-fast;
    min-width: 80px;

    &--yes {
      background-color: $primary-green;
      color: $background-white;

      &:hover {
        background-color: $primary-green-hover;
      }
    }

    &--no {
      background-color: $text-dark;
      color: $background-white;

      &:hover {
        background-color: #555;
      }
    }

    &--ok {
      background-color: $primary-green;
      color: $background-white;

      &:hover {
        background-color: $primary-green-hover;
      }
    }

    &--continue {
      background-color: $primary-green;
      color: $background-white;
      min-width: 120px;

      &:hover {
        background-color: $primary-green-hover;
      }
    }
  }

  // Success dialog specific styles
  &.success-dialog {
    max-width: 450px;

    .dialog-icon {
      background-color: $success-green;
      font-size: $font-size-xl;
    }

    h3 {
      color: $text-dark;
      margin-bottom: $spacing-md;
    }

    .success-subtitle {
      font-size: $font-size-sm;
      color: $text-medium;
      margin-bottom: $spacing-lg;
      line-height: 1.5;
    }
  }
}

// Highlighted cells for validation errors
.highlight-error {
  background-color: rgba(231, 76, 60, 0.1) !important;
  border-color: $danger-red !important;

  input,
  select {
    border-color: $danger-red !important;
    background-color: rgba(231, 76, 60, 0.05) !important;
  }
}
