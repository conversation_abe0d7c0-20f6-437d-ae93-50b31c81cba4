$medium: 900px;
$small: 540px;
$ipad: 480px;
$mobile: 320px;
$laptop: 768px;

.main-login-container {
  position: relative;
  min-height: 100vh;
}

.login-container {
  position: absolute;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-width: calc(530px + 10%);
  background: linear-gradient(
    -89deg,
    rgb(29 29 29 / 90%) 0%,
    rgb(0 0 0) 5%,
    rgb(21 21 21 / 1%) 100%
  );
  right: 0;
  height: 100vh;
  padding: 0 10%;
  color: var(--text-color);

  @media screen and (max-width: $small) {
    margin: 0;
    top: 50%;
    left: 50%;
    width: 100%;
    -ms-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
    padding: 0 30px;
    min-width: 294px;
    background: linear-gradient(
      0deg,
      rgb(29 29 29 / 47%) 0%,
      rgb(0, 0, 0) 35%,
      rgb(21 21 21 / 41%) 100%
    );
  }

  .welcome {
    text-align: left;
    font-size: 22px;
    line-height: 33px;
    font-family: 'poppinsmedium', Arial, Helvetica, sans-serif;
    font-weight: 500;
    letter-spacing: 0px;
    opacity: 1;
  }

  .title-label {
    text-align: center;
    font-size: 48px;
    line-height: 48px;
    font-family: 'poppinsbold', Arial, Helvetica, sans-serif;
    font-weight: 700;
    letter-spacing: 0px;
    text-transform: uppercase;
    opacity: 1;
    margin-bottom: 20px;
  }

  .input-container {
    background: var(--inout-background-clor);
    border-radius: 9px;
    opacity: 1;
    display: flex;
    align-items: center;
    margin-bottom: 20px;

    .eye-icon{
      color: var(--icon-color);
      font-size: 1.2rem;
      margin: 0 12px;
    }
  }

  .input-icon {
    color: var(--icon-color);
    padding: 0rem 0.75rem;
    border-right: 2px solid var(--input-boarder-color);
    font-size: 16px;

    .lock-icon{
      font-size: 23px;
    }
  }

  .login-input {
    border: none;
    display: inline-block;
    padding: 0.7rem 0.75rem;
    position: relative;
    flex: 1 1 auto;
    width: 1%;
    min-width: 0;
    border-radius: 8px;
    font-size: 14px;
    font-family: 'poppinsmedium', Arial, Helvetica, sans-serif;
  }

  .club-id-input {
    text-transform: uppercase;
  }

  .remember {
    font-size: 13px;
    line-height: 13px;
    font-family: 'poppinsmedium', Arial, Helvetica, sans-serif;
    margin-left: 20px;
  }

  .rme-check {
    margin-top: 2px;
  }

  .login-lable {
    font-size: 13px;
    line-height: 13px;
    font-family: 'poppinsmedium', Arial, Helvetica, sans-serif;
  }

  .forgot {
    font-size: 13px;
    line-height: 13px;
    font-family: 'poppinsmedium', Arial, Helvetica, sans-serif;
    text-decoration: none;
    color: var(--text-color);
    cursor: pointer;
  }

  .login-btn {
    background: var(--knod-light-green) 0% 0% no-repeat padding-box;
    border-radius: 13px;
    opacity: 1;
    width: 100%;
    border: none;
    padding: 0.7rem;
    font-family: 'poppinsmedium', Arial, Helvetica, sans-serif;
    color: var(--text-color);
    font-size: 13px/16px;
  }

  .logo {
    max-width: 100px;
  }
}
