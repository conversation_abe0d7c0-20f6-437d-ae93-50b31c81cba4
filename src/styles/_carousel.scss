$small: 480px;

.player-carousel-container {
  display: flex;
  flex-direction: row;
  align-items: center;

  @media screen and (max-width: $small) {
    width: 100%;
  }

  .player-buttons-container {
    display: flex;
    width: 100%;
    overflow: hidden;
    justify-content: space-around;
    align-items: center;

    @media screen and (max-width: $small) {
      justify-content: center;
      margin-left: 10px;
    }
  }

  .player-btn {
    color: var(--text-color);
    border: none;
    cursor: pointer;
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 4px;
    background: none;
    padding: 7px 12px;

    @media screen and (max-width: $small) {
      font-size: 0.8rem;

      .player-name {
        white-space: nowrap;
        max-width: 100px;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }

    .user-avatar {
      width: 38px;
      height: 38px;
      border-radius: 50%;
      overflow: hidden;
      margin-right: 10px;
      border: solid 1.5px;

      @media screen and (max-width: $small) {
        width: 26px;
        height: 26px;
      }
    }
  }

  .control-button {
    font-size: 30px;
    background: none;
    color: var(--text-color);
    border: none;
    cursor: pointer;
    text-align: center;
    width: 100%;

    @media screen and (max-width: $small) {
      font-size: 1rem;
    }

    &:disabled {
      cursor: not-allowed;
      opacity: 0.5;
    }
  }

  .active {
    background-color: var(--search-background-color);
    border-radius: 8px;
  }
}
