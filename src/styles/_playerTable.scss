.players-table-container {
  overflow-x: auto;
  font-family: 'poppinsmedium', Arial, Helvetica, sans-serif;

  .search-input-db {
    .sarch-bar-input {
      position: relative;

      .search-icon {
        position: absolute;
        right: 13px;
        top: 0;
      }
    }
  }

  .search-bar-container {
    width: 100%;
    height: 53px;
    background: var(--search-background-color) 0% 0% no-repeat padding-box;
    opacity: 1;
    color: var(--text-color);
    font-size: 16px;
    font-family: 'poppinsmedium', Arial, Helvetica, sans-serif;
    font-weight: 100;

    @media screen and (max-width: $small) {
      display: none;
    }

    .search-bar {
      margin-left: 8px;
      max-width: 70%;
      min-width: 180px;
      border-radius: 19px 0px 0px 19px;

      input {
        background-color: var(--search-input-color);
        border: none;
        color: var(--text-color);
        padding: 6px 9px;
        border-radius: 12px;
        font-size: 15px;

        @media screen and (max-width: $small) {
          font-size: 0.7rem;
        }

        &:focus {
          border: none;
          outline: none;
          box-shadow: none;
        }
      }

      input:-internal-autofill-selected {
        background-color: var(--search-input-color);
        border: none;
        color: var(--text-color);
      }

      ::placeholder {
        color: var(--text-color);
        opacity: 0.8;
      }

      .search-icon {
        background-color: var(--search-input-color);
        border: none;
        color: var(--text-color);
        padding: 4.4px 12px;
        border-radius: 0px 12px 12px 0px;
        font-size: 17px;

        @media screen and (max-width: $small) {
          font-size: 0.84rem;
        }
      }
    }

    .headers {
      display: flex;
      justify-content: space-around;
      align-items: center;
      min-width: 60%;
    }
  }

  .players-table {
    margin-top: 15px;
    background-color: var(--knod-dark-green);
    width: 100%;
    color: var(--text-color);
    font-size: 16px;

    .mobile-player-col {
      position: relative;
    }

    th {
      font-weight: normal;

      &:first-child {
        border-radius: 19px 0px 0px 19px;
      }

      &:last-child {
        border-radius: 0px 19px 19px 0px;
      }
    }

    .player-avatar-td {
      width: 60px;

      @media screen and (max-width: $small) {
        width: 40px;
      }
    }

    .player-profile {
      display: flex;
      flex-direction: row;
      align-items: center;
      padding: 4px;

      .user-avatar {
        width: 42px;
        height: 42px;
        border-radius: 50%;
        overflow: hidden;
        margin-right: 10px;
        border: solid 1.6px;

        @media screen and (max-width: $small) {
          width: 28px;
          height: 28px;
        }
      }
    }

    .palyerName {
      font-size: 16px;
      font-family: 'poppinsmedium', Arial, Helvetica, sans-serif;

      @media screen and (max-width: $small) {
        font-size: 0.7rem;
        width: 118px;
        padding-right: 15px;
        border-right: 1px solid var(--search-background-color);
        position: relative;
      }
    }

    .inc-dec-td {
      @media screen and (max-width: $small) {
        padding-left: 10px;
      }
    }

    .inc-dec-container {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: center;

      input {
        background-color: var(--search-background-color);
        border: none;
        color: var(--count-input-color);
        max-width: 55px;
        text-align: center;
        padding: 4.2px 3px 1.8px 3px;
        font-size: 16px;
        margin: 4px;
        border-radius: 7px;
        font-family: 'poppinsmedium', Arial, Helvetica, sans-serif;

        @media screen and (max-width: $small) {
          max-width: 35px;
          padding: 4.2px 3px 1.8px 3px;
          font-size: 12px;
          margin: 3px;
          border-radius: 5px;
        }

        &:disabled,
        &[disabled] {
          opacity: 0.58;
        }
      }

      button {
        border: none;
        color: var(--text-color);
        font-size: 24px;
        padding: 5.5px 5px 5px 4.5px;
        margin: 4px 10px;
        border-radius: 7px;
        background-color: var(--search-background-color);
        text-align: center;
        height: 24px;
        width: 24px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        &:disabled,
        &[disabled] {
          opacity: 0.6;
        }
      }

      @media screen and (max-width: $small) {
        button {
          font-size: 20px;
          padding: 5px 4px 3px 4px;
          margin: 3px;
          height: 22px;
          width: 22px;
        }
      }
    }

    .graph-icon {
      padding: 0 20px 0 0;
      font-size: 20px;

      button {
        background-color: var(--knod-light-green);
        padding: 0px 4px;
        border: none;
        border-radius: 5px;
        color: var(--text-color);
      }

      @media screen and (max-width: $small) {
        font-size: 0.7rem;
        padding: 0 10px 0 0;
      }
    }

    .playerCheckBox {
      width: 40px;

      @media screen and (max-width: $small) {
        min-width: 30px;
        max-width: 30px;
      }
    }

    .mobile-table-head {
      font-size: 14px;

      @media screen and (min-width: $small) {
        display: none;
      }
    }

    .hide-column {
      @media screen and (max-width: $small) {
        display: none;
      }
    }
  }

  .mobile-search-bar {
    margin-left: 8px;
    max-width: 70%;
    min-width: 180px;
    border-radius: 19px 0px 0px 19px;
    position: relative;

    @media screen and (min-width: $small) {
      display: none;
    }

    input {
      background-color: var(--search-background-color);
      border: none;
      color: var(--text-color);
      padding: 6px 9px;
      border-radius: 12px;
      font-size: 15px;
    }

    input:focus {
      border: none;
    }

    input:-internal-autofill-selected {
      background-color: var(--search-input-color);
      border: none;
      color: var(--text-color);
    }

    ::placeholder {
      color: var(--text-color);
      opacity: 0.8;
    }

    .search-icon {
      background-color: var(--search-background-color);
      border: none;
      color: var(--text-color);
      padding: 4.4px 12px;
      border-radius: 12px;
      font-size: 17px;
      position: absolute;
      right: 0;
      top: 0;
      max-width: 32px;
    }
  }

  .mobile-slide-btn {
    position: absolute;
    right: -22px;
    top: 220px;
    -ms-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
    background-color: var(--search-background-color);
    color: var(--text-color);
    width: 23px;
    height: 23px;
    display: inline-block;
    font-size: 15px;
    border-radius: 50%;
    border: none;
    z-index: 99;

    @media screen and (min-width: $small) {
      display: none;
    }
  }
}
