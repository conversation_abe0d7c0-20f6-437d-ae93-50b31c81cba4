.match-dashboard {
  min-height: 100vh;
  background-color: #f8f9fa;
  display: flex;
  flex-direction: row;

  &__back-button {
    position: absolute;
    top: 20px;
    left: 20px;
    height: 40px;
    padding: 0 16px;
    background-color: #ffffff;
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    z-index: 10;

    &:hover {
      background-color: #f8f9fa;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      transform: translateY(-1px);
    }

    svg {
      width: 18px;
      height: 18px;
      color: #333;
    }

    span {
      font-size: 14px;
      font-weight: 500;
      color: #333;
      white-space: nowrap;
    }
  }

  .logoArea {
    margin-bottom: 60px;
    margin-top: 60px;

    .dashboard-logo {
      height: 100px;
    }
  }

  &-left {
    position: relative;
    display: flex;
    flex-direction: column;
    padding: 0 20px;
    align-items: center;
    background-color: #ededed;
  }

  &-right {
    padding: 0 20px;

    .teamName {
      color: black;
      font-size: 2rem;
      margin: 90px 0;
      font-weight: bold;
    }
  }

  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 40px;
  }

  &__brand {
    display: flex;
    align-items: center;
    gap: 10px;

    &-icon {
      width: 50px;
      height: 50px;
      background-color: #4a9d7a;
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-weight: bold;
      font-size: 20px;
    }

    &-title {
      font-size: 24px;
      font-weight: bold;
      color: #333;
      margin: 0;
    }
  }

  &__selected-team {
    font-size: 28px;
    font-weight: bold;
    color: #333;
    margin: 0;
  }

  &__content {
    display: flex;
    gap: 40px;
  }

  &__sidebar {
    width: 280px;
    display: flex;
    flex-direction: column;
    gap: 30px;
    height: calc(100vh - 140px); // Fit within viewport height
  }

  &__search {
    position: relative;

    &-input {
      width: 100%;
      padding: 12px 40px 12px 16px;
      border: 1px solid #e0e0e0;
      border-radius: 12px;
      font-size: 16px;
      background-color: white;
      outline: none;

      &:focus {
        border-color: #4a9d7a;
        box-shadow: 0 0 0 3px rgba(74, 157, 122, 0.1);
      }
    }

    &-icon {
      position: absolute;
      right: 12px;
      top: 50%;
      transform: translateY(-50%);
      color: #666;
    }
  }

  &__team-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0; // Important for flex child to shrink

    h3 {
      font-size: 16px;
      color: #4a9d7a;
      margin-bottom: 12px;
      font-weight: 500;
      flex-shrink: 0; // Prevent title from shrinking
    }
  }

  &__team-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
    flex: 1; // Take remaining space
    overflow-y: auto; // Enable vertical scrolling
    padding-right: 8px; // Add some padding for scrollbar space

    // Custom scrollbar styling
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 10px;
    }

    &::-webkit-scrollbar-thumb {
      background: #4a9d7a;
      border-radius: 10px;

      &:hover {
        background: #3d8464;
      }
    }

    // For Firefox
    scrollbar-width: thin;
    scrollbar-color: #4a9d7a #f1f1f1;
  }

  &__team-button {
    padding: 12px 16px;
    border-radius: 12px;
    border: none;
    background-color: transparent;
    color: #333;
    text-align: left;
    cursor: pointer;
    font-size: 16px;
    font-weight: 400;
    transition: all 0.2s ease;
    flex-shrink: 0; // Prevent buttons from shrinking

    &:hover {
      background-color: rgba(74, 157, 122, 0.1);
    }

    &--selected {
      background-color: #4a9d7a;
      color: white;
      font-weight: 600;

      &:hover {
        background-color: #3d8464;
      }
    }
  }

  &__main {
    flex: 1;
    height: calc(100vh - 140px); // Fit within viewport height
    overflow-y: auto; // Enable vertical scrolling for main content
    padding-right: 8px; // Add padding for scrollbar space

    // Custom scrollbar styling for main content
    &::-webkit-scrollbar {
      width: 8px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 10px;
    }

    &::-webkit-scrollbar-thumb {
      background: #4a9d7a;
      border-radius: 10px;

      &:hover {
        background: #3d8464;
      }
    }

    // For Firefox
    scrollbar-width: thin;
    scrollbar-color: #4a9d7a #f1f1f1;
  }

  &__month-filters {
    display: flex;
    gap: 8px;
    margin-bottom: 30px;
    // flex-wrap: wrap;
    flex-shrink: 0; // Prevent month filters from shrinking
    position: sticky; // Keep month filters visible when scrolling
    top: 0;
    background-color: #f8f9fa; // Match dashboard background
    padding: 10px 0;
    z-index: 10;
    width: 100%;
    max-width: 1150px;
    overflow: auto;
  }

  &__month-button {
    padding: 12px 24px;
    border-radius: 25px;
    border: none;
    background-color: white;
    color: #333;
    cursor: pointer;
    font-size: 16px;
    font-weight: 500;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    }

    &--selected {
      background-color: #4a9d7a;
      color: white;
      box-shadow: none;

      &:hover {
        background-color: #3d8464;
        transform: none;
      }
    }
  }

  &__matches-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 20px;
  }

  &__match-card {
    background-color: white;
    border-radius: 16px;
    padding: 24px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border: 1px solid #f0f0f0;
    transition: transform 0.2s ease, box-shadow 0.2s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
    }

    &__title {
      font-size: 20px;
      font-weight: bold;
      color: #333;
      margin-bottom: 8px;
    }

    &__competition {
      color: #4a9d7a;
      font-size: 14px;
      font-weight: 600;
      margin-bottom: 16px;
    }

    &__details {
      display: flex;
      flex-direction: column;
      gap: 8px;
      margin-bottom: 20px;
    }

    &__detail-row {
      display: flex;
      gap: 12px;

      &-label {
        font-weight: 600;
        color: #333;
        min-width: 70px;
      }

      &-value {
        color: #666;
      }
    }

    &__action-button {
      padding: 12px 24px;
      border-radius: 25px;
      border: none;
      color: white;
      cursor: pointer;
      font-size: 14px;
      font-weight: 600;
      transition: all 0.2s ease;

      &--stats {
        background-color: #4a9d7a;

        &:hover {
          background-color: #3d8464;
          transform: translateY(-1px);
        }
      }

      &--report {
        background-color: #333;

        &:hover {
          background-color: #1a1a1a;
          transform: translateY(-1px);
        }
      }

      &--report-download {
        background-color: #333;
        margin-left: 20px;

        &:hover {
          background-color: #1a1a1a;
          transform: translateY(-1px);
        }
      }
    }
  }

  // Loading spinner styles
  &__loading {
    margin-top: 1rem;
    width: 100%;
    display: flex;
    justify-content: center;
  }

  // Error message styling - centered text for "No Events Available" and "No Teams Available"
  .error {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 40px 20px;
    color: #666;
    font-size: 16px;
    font-weight: 500;
    text-align: center;
    min-height: 200px; // Give it some height so it's visually prominent
  }
}

// Responsive design
@media (max-width: 768px) {
  .match-dashboard {
    padding: 15px;

    &__content {
      flex-direction: column;
      gap: 20px;
    }

    &__sidebar {
      width: 100%;
      height: auto; // Reset height on mobile
    }

    &__team-list {
      max-height: 250px; // Set specific height on mobile
      flex: none; // Reset flex on mobile
    }

    &__main {
      height: auto; // Reset height on mobile
      overflow-y: visible; // Reset overflow on mobile
      padding-right: 0; // Remove padding on mobile
    }

    &__matches-grid {
      grid-template-columns: 1fr;
    }

    &__month-filters {
      justify-content: center;
    }

    &__header {
      flex-direction: column;
      gap: 15px;
      text-align: center;
    }

    // Adjust error message for mobile
    .error {
      min-height: 150px;
      padding: 30px 15px;
    }
  }
}
