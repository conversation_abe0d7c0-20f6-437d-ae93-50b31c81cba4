$greeting-color: #05b261;
$medium: 900px;
$small: 540px;

.top-navbar {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  background-color: none;
  height: 87px;
  padding: 16px 4px;
  color: var(--text-color);

  @media screen and (max-width: $small) {
    padding: 10px 0px;
    height: 60px;
  }
}

.user-profile {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-weight: bold;
}

.user-avatar {
  width: 43px;
  height: 43px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 10px;

  @media screen and (max-width: $small) {
    width: 42px;
    height: 42px;
  }
}

.user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.user-name {
  font-size: 1rem;
  font-family: "poppinsmedium", Arial, Helvetica, sans-serif;

  @media screen and (max-width: $small) {
    font-size: 0.9rem;
  }
}

.logout-btn-container {
  position: absolute;
  top: 87px;
  right: 25px;
}

.logout-btn {
  background: var(--navbar-color) 0% 0% no-repeat padding-box;
  border-radius: 18px;
  color: var(--text-color);
  border: none;
  padding: 0.7rem;
  text-decoration: none;
}

.greeting {
  text-align: center;
  font-size: 14px;
  letter-spacing: 0px;
  color: $greeting-color;
  opacity: 1;
  margin-bottom: 0;
  text-align: right;
  font-family: "poppinsmedium", Arial, Helvetica, sans-serif;
}
