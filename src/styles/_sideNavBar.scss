$small: 480px;

.side-navbar {
  background-color: var(--navbar-color);
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;

  .side-bar {
    width: 80%;

    @media screen and (max-width: $small) {
      display: none;
    }

    .logo-container {
      margin-top: 30px;
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
    }

    .logo {
      max-width: 40px;
      height: auto;
    }

    .title-label {
      text-align: center;
      font-size: 25px;
      line-height: 35px;
      font-family: 'futura_stdheavy', Arial, Helvetica, sans-serif;
      font-weight: 700;
      letter-spacing: 0px;
      opacity: 1;
      color: var(--text-color);
      font-family: 'futura_stdheavy', Arial, Helvetica, sans-serif;
    }

    .title-label-2 {
      font-size: 16px;
      line-height: 25px;
      width: 80%;
      font-family: Poppins;
      letter-spacing: 0px;
      opacity: 1;
      color: var(--text-color);
      font-family: 'poppinsmedium', Arial, Helvetica, sans-serif;
    }

    .button-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      font-family: 'poppinsmedium', Arial, Helvetica, sans-serif;

      .nav-button {
        background: var(--nav-button-color) 0% 0% no-repeat padding-box;
        border-radius: 8px;
        opacity: 1;
        min-width: 100%;
        border: none;
        margin-top: 20px;
        padding: 0.8rem;
        color: var(--text-color);
        text-align: left;
        font-size: 17px/26px;
        text-decoration: none;
        font-weight: normal;
      }

      .nav-button:hover {
        background-color: var(--knod-light-green);
      }

      .active {
        background-color: var(--knod-light-green);
      }
    }

    .save-btn {
      background: var(--knod-light-green) 0% 0% no-repeat padding-box;
      border-radius: 8px;
      opacity: 1;
      border: none;
      padding: 0.8rem;
      color: var(--text-color);
      text-align: left;
      font-size: 17px/26px;
      position: relative;
      min-width: 100%;
      text-align: center;
      font-family: 'poppinsmedium', Arial, Helvetica, sans-serif;
      font-weight: normal;
    }

    .save-btn:disabled,
    .save-btn[disabled] {
      background-color: var(--disabled-btn-color);
    }

    .save-spinner {
      margin-top: 50px;
      color: var(--text-color);
    }
  }

  .horizontal-line {
    margin-top: 20px;
    background-color: var(--knod-dark-green);
    height: 1px;
    min-width: 100%;
    margin-right: calc(var(--bs-gutter-x) * -0.5);
    margin-left: calc(var(--bs-gutter-x) * -0.5);
  }

  .chart-form {
    input {
      background-color: var(--search-input-color);
      border: none;
      color: var(--text-color);
      padding: 10px 18px;
      font-size: 15px;
      border-radius: 8px;
      opacity: 1;
      border: none;
      margin-top: 10px;
      padding: 0.8rem;
      position: relative;
      width: 100%;
    }

    ::placeholder {
      color: var(--text-color);
      opacity: 0.8;
    }

    input[type='date']::-webkit-calendar-picker-indicator {
      filter: invert(2);
    }
  }

  .save-alert {
    top: 0px;
    left: 0px;
    right: 0px;
    position: absolute;
    width: 30%;
    font-size: 1.1rem;
    margin: 0 auto;
    z-index: 999;

    .alert-success {
      background: var(--knod-light-green);
      color: var(--text-color);
      border: none;
    }
  }
}

.input-container {
  position: relative;

  .date-input-container {
    position: relative;
    display: flex;

    .icon {
      position: absolute;
      right: 16px;
      top: 56%;
      transform: translateY(-50%);
      cursor: pointer;
      color: var(--text-color);
      font-size: 13px;
    }
  }
}
