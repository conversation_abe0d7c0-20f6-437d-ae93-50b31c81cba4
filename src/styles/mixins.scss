@import "./variables.scss";

// Common mixins for the MatchStatsTable component

// Input field styling mixin
@mixin input-field {
  width: 100%;
  border: 1px solid $border-light;
  border-radius: $border-radius-sm;
  padding: $spacing-xs $spacing-sm;
  font-size: $font-size-sm;
  background-color: transparent;
  transition: all $transition-fast;

  &:focus {
    outline: none;
    border-color: $primary-green;
    box-shadow: $focus-shadow;
    background-color: rgba(255, 255, 255, 0.8);
  }

  &:hover:not(:focus) {
    border-color: #b0b0b0;
  }
}

// Button styling mixin
@mixin button-primary {
  background-color: $primary-green;
  color: $background-white;
  border: none;
  border-radius: $border-radius-sm;
  padding: $spacing-sm $spacing-md;
  font-size: $font-size-sm;
  font-weight: $font-weight-semibold;
  cursor: pointer;
  transition: background-color $transition-fast;

  &:hover {
    background-color: $primary-green-hover;
  }

  &:disabled {
    background-color: $text-light;
    cursor: not-allowed;
  }
}

// Card container mixin
@mixin card-container {
  background-color: $background-white;
  border-radius: $border-radius-lg;
  box-shadow: $shadow-sm;
  overflow: hidden;
}

// Flex center mixin
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

// Text ellipsis mixin
@mixin text-ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

// Screen reader only content
@mixin sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

// Responsive breakpoints
@mixin mobile {
  @media (max-width: 768px) {
    @content;
  }
}

@mixin tablet {
  @media (min-width: 769px) and (max-width: 1024px) {
    @content;
  }
}

@mixin desktop {
  @media (min-width: 1025px) {
    @content;
  }
}

// Hover effect for interactive elements
@mixin hover-lift {
  transition: transform $transition-fast, box-shadow $transition-fast;

  &:hover {
    transform: translateY(-1px);
    box-shadow: $shadow-md;
  }
}
