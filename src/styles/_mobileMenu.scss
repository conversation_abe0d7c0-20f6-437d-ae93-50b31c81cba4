$small: 480px;

.dropdown {
  display: inline-block;
  width: 83vw;
  position: relative;
  left: 50%;
  top: 26.8px;
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  z-index: 2;
  display: none;
  color: var(--text-color);
  text-align: center;

  @media screen and (max-width: $small) {
    display: block;
  }

  .lable {
    font-size: 14px;
    font-family: 'poppinsmedium', Arial, Helvetica, sans-serif;
  }

  .dropdown-button {
    cursor: pointer;
    background: var(--knod-light-green) 0% 0% no-repeat padding-box;
    border-radius: 9px;
    opacity: 1;
    width: 100%;
    border: none;
    padding: 0.4rem 0.6rem;
    color: var(--text-color);
    font-size: 17px/26px;
    font-family: 'poppinsmedium', Arial, Helvetica, sans-serif;
    font-weight: normal;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .dropdown-content {
    position: absolute;
    background-color: var(--search-background-color);
    box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2);
    width: 95%;
    left: 0;
    top: 63px;
    right: 0;
    margin: 0 auto;
    border-radius: 5px;
  }

  .dropdown-content a {
    display: block;
    padding: 12px 16px;
    text-decoration: none;
    color: var(--text-color);
    font-family: 'poppinsmedium', Arial, Helvetica, sans-serif;
    font-size: 17px/26px;
    border-radius: 5px;

    &:hover {
      background: var(--knod-light-green) 0% 0% no-repeat padding-box;
    }
  }
}

.mobile-form {
  display: none;

  @media screen and (max-width: $small) {
    display: block;
  }

  .save-btn {
    background: var(--knod-light-green) 0% 0% no-repeat padding-box;
    border-radius: 8px;
    opacity: 1;
    border: none;
    margin-top: 20px;
    padding: 0.5rem;
    color: var(--text-color);
    text-align: left;
    font-size: 14px;
    position: relative;
    min-width: 100%;
    text-align: center;
  }

  .save-btn:disabled,
  .save-btn[disabled] {
    background-color: var(--disabled-btn-color);
  }

  .save-spinner {
    margin-top: 50px;
    color: var(--text-color);
  }

  .save-alert {
    top: 30%;
    left: 0px;
    right: 0px;
    position: absolute;
    width: 90%;
    font-size: 0.9rem;
    margin: 0 auto;
    z-index: 999;

    .alert-success {
      background: var(--knod-light-green);
      color: var(--text-color);
      border: none;
    }
  }

  .title-label {
    font-size: 1rem;
    font-family: 'poppinsmedium', Arial, Helvetica, sans-serif;
    letter-spacing: 0px;
    opacity: 1;
    color: var(--text-color);
    text-align: center;
  }

  .chart-form {
    display: flex;
    flex-direction: column;
    justify-content: center;

    input {
      background-color: var(--search-background-color);
      border: none;
      color: var(--text-color);
      padding: 10px 18px;
      font-size: 15px;
      border-radius: 8px;
      opacity: 1;
      border: none;
      margin-top: 10px;
      padding: 0.8rem;
      position: relative;
      width: 100%;
    }

    ::placeholder {
      color: var(--text-color);
      opacity: 0.8;
    }

    input[type='date']::-webkit-calendar-picker-indicator {
      filter: invert(2);
    }
  }
}
