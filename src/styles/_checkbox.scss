.checkbox {
  display: block;
  position: relative;
  padding-left: 10px;
  margin-bottom: 12px;
  cursor: pointer;
  font-size: 22px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;

  .checkmark {
    position: absolute;
    top: 0;
    left: 0;
    height: 25px;
    width: 25px;
    background-color: var(--search-background-color);
    margin-top: -5.5px;
    border-radius: 3px;

    @media screen and (max-width: $small) {
      height: 20px;
      width: 20px;
    }
  }

  .login-check {
    position: absolute;
    top: 0;
    left: 0;
    height: 17px;
    width: 17px;
    background-color: var(--knod-light-blue);
    margin-top: -5.5px;
    border-radius: 3px;

    @media screen and (max-width: $small) {
      height: 20px;
      width: 20px;
    }

    &.unchecked {
      background-color: var(--text-color);
    }
  }

  .dashboard-check {
    position: absolute;
    top: 0;
    left: 0;
    height: 17px;
    width: 17px;
    background-color: var(--knod-light-green);
    margin-top: -5.5px;
    border-radius: 3px;

    @media screen and (max-width: $small) {
      height: 20px;
      width: 20px;
    }

    &.unchecked {
      background-color: var(--search-background-color);
    }
  }

  input {
    position: absolute;
    opacity: 1;
    cursor: pointer;
    height: 0;
    width: 0;
  }

  input:checked ~ {
    .checkbox {
      &::after {
        display: block;
      }
    }
  }
}

.active-check {
  &::after {
    content: '';
    position: absolute;
    left: 5px;
    top: -4px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 7px;
    height: 12px;
    border: solid var(--text-color);
    border-width: 0 3px 3px 0;
    transform: rotate(45deg);

    @media screen and (max-width: $small) {
      left: 7.5px;
      top: -2.8px;
      width: 6px;
      height: 12.5px;
      border-width: 0 3px 3px 0;
    }
  }
}
