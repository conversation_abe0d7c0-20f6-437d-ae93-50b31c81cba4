$search-background-color: #0e2434;

.pagination {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
  padding: 0 3vw 0 0;

  button {
    border: none;
    color: var(--text-color);
    font-size: 14px;
    padding: 5px;
    margin: 4px;
    border-radius: 7px;
    background-color: $search-background-color;
    text-align: center;
    height: 26px;
    width: 26px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }

  .pagination-btn {
    &:hover {
      background-color: var(--knod-light-green);
      border-color: var(--knod-light-green);
    }
  }

  button.active {
    background-color: var(--knod-light-green);
    border-color: var(--knod-light-green);
  }
}
