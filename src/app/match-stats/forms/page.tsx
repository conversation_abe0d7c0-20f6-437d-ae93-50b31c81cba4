"use client";

import React from "react";
import { useSearchParams } from "next/navigation";
import AuthLayout from "../../component/AuthLayout";
import MatchStatsTable from "@/app/component/match-stats/MatchStatsTable";

export default function ManageSubscription() {
  const searchParams = useSearchParams();
  const opponentIds = searchParams.get("opponentIds") || undefined;
  const teamId = searchParams.get("teamId") || undefined;
  const matchId = searchParams.get("matchId") || undefined;
  const teamName = searchParams.get("teamName") || undefined;

  return (
    <div>
      <AuthLayout>
        <MatchStatsTable mode="edit" matchId={matchId} opponentIds={opponentIds} teamId={teamId} teamName={teamName} />
      </AuthLayout>
    </div>
  );
}
