'use client';

import Image from 'next/image';
import React, { useEffect, useState } from 'react';
import { useRouter, redirect } from 'next/navigation';
import { useDispatch, useSelector } from 'react-redux';
import { MdEmail } from 'react-icons/md';
import { BiSolidLock } from 'react-icons/bi';
import { FaRegIdCard } from 'react-icons/fa';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faIdCard } from '@fortawesome/free-solid-svg-icons';
import { AiFillEye, AiFillEyeInvisible } from 'react-icons/ai';
import { Alert } from 'react-bootstrap';
import Spinner from 'react-bootstrap/Spinner';
import { getLoginState } from '@/api/amplifyApi';
import { LOGGED, NOTLOGGED } from '@/const/loginState';
import { clubIdPattern, emailPattern } from '@/const/regexPattern';
import { AppDispatch, RootState } from '@/redux/store';
import Loader from '@/app/component/Loader';
import CheckBox from '@/app/component/CheckBox';
import {
  setLoginState,
  signInAndFetchUser,
  setErrorMessage,
  configureAmplify,
} from '@/redux/slices/authReducer';

export default function Login() {
  const dispatch = useDispatch<AppDispatch>();
  const router = useRouter();

  const { isLoading, errorMessage, isAmplifyConfigured, loginState } =
    useSelector((state: RootState) => state?.auth);

  const [formData, setFormData] = useState({
    clubId: '',
    email: '',
    password: '',
    rememberMe: false,
  });

  const [showPassword, setShowPassword] = useState(false);

  const handleChange = (event: { target: { name: string; value: string } }) => {
    const { name, value } = event.target;

    const newFormData = { ...formData, [name]: value };

    setFormData(newFormData);
    dispatch(setErrorMessage(''));
  };

  const handleRememberChange = (name: string, checked: boolean) => {
    const newFormData = { ...formData, [name]: checked };

    setFormData(newFormData);
  };

  const handleLoginClick = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();

    if (validateLoginForm()) {
      try {
        await dispatch(signInAndFetchUser(formData));
      } catch (error) {
        console.log(error);
      }
    }
  };

  const validateLoginForm = () => {
    if (!clubIdPattern.test(formData?.clubId)) {
      dispatch(setErrorMessage('Entered club id is invalid!'));
      return false;
    }

    if (!emailPattern.test(formData?.email)) {
      dispatch(setErrorMessage('Entered email address is invalid!'));
      return false;
    }

    if (!formData.password.length) {
      dispatch(setErrorMessage('Entered password is invalid!'));
      return false;
    }

    return true;
  };

  const handleTogglePassword = () => {
    setShowPassword((prevShowPassword) => !prevShowPassword);
  };

  useEffect(() => {
    (async () => {
      await dispatch(configureAmplify());

      const userLoginState = await getLoginState();

      dispatch(setLoginState(userLoginState));
    })();
  }, []);

  useEffect(() => {
    if (loginState === LOGGED) {
      redirect('/');
    }
  }, [loginState]);

  return (
    <>
      {loginState !== NOTLOGGED && <Loader />}
      <div className="main-login-container">
        <Image
          fill
          src="/images/mask_group7.png"
          alt="Login Image"
          className="z-0"
          style={{ objectFit: 'cover' }}
        />
        <div className="login-container">
          <img src="/images/logo.png" alt="" className="logo" />
          <h2 className="welcome">Welcome to</h2>
          <h1 className="title-label">
            KOACH HUB
          </h1>
          {errorMessage && (
            <div className="w-100">
              <Alert key={'danger'} variant={'danger'}>
                {errorMessage}
              </Alert>
            </div>
          )}
          <form className="w-100" method="post" onSubmit={handleLoginClick}>
            <div className="input-container">
              <span className="input-icon">
              <FontAwesomeIcon icon={faIdCard} />
              </span>
              <input
                type="text"
                id="clubId"
                value={formData?.clubId}
                name="clubId"
                className="login-input club-id-input "
                placeholder="Club ID"
                onChange={handleChange}
              />
            </div>
            <div className="input-container">
              <span className="input-icon">
                <MdEmail />
              </span>
              <input
                type="text"
                id="email"
                value={formData?.email}
                name="email"
                className="login-input"
                placeholder="Email Address"
                onChange={handleChange}
              />
            </div>
            <div className="input-container">
              <span className="input-icon">
                <BiSolidLock className="lock-icon"/>
              </span>
              <input
                type={showPassword ? 'text' : 'password'}
                id="password"
                name="password"
                className="login-input"
                placeholder="Password"
                value={formData?.password}
                onChange={handleChange}
              />
              <span className="eye-icon" onClick={handleTogglePassword}>
                {showPassword ? <AiFillEyeInvisible /> : <AiFillEye />}
              </span>
            </div>
            <div className="d-flex align-items-center justify-content-between mt-4 mb-3">
              <div className="d-flex align-items-start">
                <div className="d-flex align-items-center h-5 rme-check">
                  <CheckBox
                    onChange={handleRememberChange}
                    name="rememberMe"
                    value={formData?.rememberMe}
                    checkPage="login-check"
                  />
                </div>
                <label className="remember">Remember Me</label>
              </div>
              <a href="/forgot-password" className="forgot">
                Forgot Password?
              </a>
            </div>
            <div>
              {isLoading ? (
                <div className="mt-4 pt-1 d-flex justify-content-center">
                  <Spinner animation="border" />
                </div>
              ) : (
                <button className="login-btn">Sign In</button>
              )}
            </div>
          </form>
        </div>
      </div>
    </>
  );
}
