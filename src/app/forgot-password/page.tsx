'use client';

import { useEffect, useState, MouseEvent } from 'react';
import Image from 'next/image';
import { useDispatch, useSelector } from 'react-redux';
import { redirect } from 'next/navigation';
import { Alert } from 'react-bootstrap';
import Spinner from 'react-bootstrap/Spinner';
import { MdEmail } from 'react-icons/md';
import { BiSolidLock } from 'react-icons/bi';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faIdCard } from '@fortawesome/free-solid-svg-icons';
import { AiFillEye, AiFillEyeInvisible } from 'react-icons/ai';
import { AppDispatch, RootState } from '@/redux/store';
import { getLoginState } from '@/api/amplifyApi';
import { LOGGED, NOTLOGGED } from '@/const/loginState';
import { clubIdPattern, emailPattern } from '@/const/regexPattern';
import Loader from '@/app/component/Loader';
import {
  forgotPasswordSubmit,
  setErrorMessage,
  forgotPassword,
  setLoginState,
} from '@/redux/slices/authReducer';

export default function ForgotPassword() {
  const dispatch = useDispatch<AppDispatch>();

  const { isLoading, errorMessage, hasSentResetLink, loginState } = useSelector(
    (state: RootState) => state?.auth
  );

  const [formData, setFormData] = useState({
    clubId: '',
    email: '',
    password: '',
    code: '',
  });

  const [showPassword, setShowPassword] = useState(false);

  const handleChange = (event: { target: { name: string; value: string } }) => {
    const { name, value } = event.target;

    const newFormData = { ...formData, [name]: value };

    setFormData(newFormData);
    dispatch(setErrorMessage(''));
  };

  const handleChangePassword = async (event: MouseEvent<HTMLButtonElement>) => {
    event.preventDefault();

    if (validateLoginForm()) {
      try {
        await dispatch(forgotPasswordSubmit(formData));
      } catch (error) {
        console.log(error);
      }
    }
  };

  const handleForgotPassword = async (event: MouseEvent<HTMLButtonElement>) => {
    event.preventDefault();

    if (!clubIdPattern.test(formData?.clubId)) {
      dispatch(setErrorMessage('Entered club id is invalid!'));
    } else if (!emailPattern.test(formData?.email)) {
      dispatch(setErrorMessage('Entered email address is invalid!'));
    } else {
      try {
        await dispatch(forgotPassword(formData));
      } catch (error) {
        console.log(error);
      }
    }
  };

  const validateLoginForm = () => {
    if (!clubIdPattern.test(formData?.clubId)) {
      dispatch(setErrorMessage('Entered club id is invalid!'));
      return false;
    }

    if (!emailPattern.test(formData?.email)) {
      dispatch(setErrorMessage('Entered email address is invalid!'));
      return false;
    }

    if (!formData.password.length) {
      dispatch(setErrorMessage('Entered password is invalid!'));
      return false;
    }

    if (!formData.code.length) {
      dispatch(setErrorMessage('Entered verification is invalid!'));
      return false;
    }

    return true;
  };

  const handleTogglePassword = () => {
    setShowPassword((prevShowPassword) => !prevShowPassword);
  };

  useEffect(() => {
    (async () => {
      const userLoginState = await getLoginState();

      dispatch(setLoginState(userLoginState));
    })();
  }, []);

  useEffect(() => {
    if (loginState === LOGGED) {
      redirect('/');
    }
  }, [loginState]);

  return (
    <>
      {loginState !== NOTLOGGED && <Loader />}
      <div className="main-login-container">
        <Image
          fill
          src="/images/mask_group7.png"
          alt="Login Image"
          className="z-0"
          style={{ objectFit: 'cover' }}
        />
        <div className="login-container">
          <img src="/images/logo.png" alt="" className="logo" />
          <h2 className="welcome">Forgot Password</h2>
          {errorMessage && (
            <div className="w-100">
              <Alert key={'danger'} variant={'danger'}>
                {errorMessage}
              </Alert>
            </div>
          )}
          <form className="w-100" method="post">
            <div className="input-container">
              <span className="input-icon">
              <FontAwesomeIcon icon={faIdCard} />
              </span>
              <input
                type="text"
                id="clubId"
                value={formData?.clubId}
                name="clubId"
                className="login-input"
                placeholder="Club ID"
                onChange={handleChange}
              />
            </div>
            <div className="input-container">li
              <span className="input-icon">
                <MdEmail />
              </span>
              <input
                type="text"
                id="email"
                value={formData?.email}
                name="email"
                className="login-input"
                placeholder="Email Address"
                onChange={handleChange}
              />
            </div>
            {hasSentResetLink && (
              <div className="mb-2">
                <div className="input-container">
                  <span className="input-icon">
                    <BiSolidLock />
                  </span>
                  <input
                    type="text"
                    id="code"
                    name="code"
                    value={formData?.code}
                    className="login-input"
                    placeholder="Verification code"
                    onChange={handleChange}
                  />
                </div>
                <div className="input-container">
                  <span className="input-icon">
                    <BiSolidLock />
                  </span>
                  <input
                    type={showPassword ? 'text' : 'password'}
                    id="password"
                    name="password"
                    value={formData?.password}
                    className="login-input"
                    placeholder="New password"
                    onChange={handleChange}
                  />
                  <span className="eye-icon" onClick={handleTogglePassword}>
                    {showPassword ? <AiFillEyeInvisible /> : <AiFillEye />}
                  </span>
                </div>
              </div>
            )}
            <div className="d-flex align-items-center justify-content-end mb-2 me-2">
              <a className="forgot" href="/login">
                Back to login
              </a>
            </div>
            {isLoading ? (
              <div className="mt-4 d-flex justify-content-center">
                <Spinner animation="border" />
              </div>
            ) : hasSentResetLink ? (
              <button className="login-btn" onClick={handleChangePassword}>
                Change Password
              </button>
            ) : (
              <button className="login-btn" onClick={handleForgotPassword}>
                Send Verification Code
              </button>
            )}
          </form>
        </div>
      </div>
    </>
  );
}
