@import '../styles/loginPage';
@import '../styles/navBar';
@import '../styles/home';
@import '../styles/loader';
@import '../styles/sideNavBar';
@import '../styles/dashboard';
@import '../styles/checkbox';
@import '../styles/mobileMenu';
@import '../styles/pagination';
@import '../styles/carousel';
@import '../styles/team-cards';
@import '../styles/fonts';
@import '../styles/playerTable';
@import '../styles/match-dashboard';

$small: 480px;

:root {
  --small: 480px;
  --medium: 900px;

  --foreground-rgb: 255, 255, 255;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;

  --text-color: #ffffff;
  --icon-color: #727272;
  --input-boarder-color: #d8d8d8;
  --inout-background-clor: #ffffff;

  --knod-light-green: #36d982;
  --knod-light-blue: #1dc4d2;
  --knod-dark-green: #183e55;
  --knod-green-hover: #05b261;
  --knod-dark-green-rgb: 24, 62, 85;

  --nav-button-color: #23344b;
  --navbar-color: #121d2e;

  --count-input-color: #faa630;
  --search-background-color: #0e2434;
  --search-input-color: #134158;

  --disabled-btn-color: #6c757d;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
}

body {
  color: rgb(var(--foreground-rgb));
  background: rgb(var(--knod-dark-green-rgb));
  font-family: 'Poppins', sans-serif;
  font-size: 1rem;

  @media screen and (max-width: $small) {
    font-size: 1rem;
  }
}

.login-check-spiner {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
}
