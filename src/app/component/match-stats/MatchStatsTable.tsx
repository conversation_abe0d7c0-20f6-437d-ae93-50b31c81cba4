import { RootState } from "@/redux/store";
import { useEffect, useState, useCallback, useMemo, memo } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useRouter } from "next/navigation";
import {
  getOpponents,
  getPlayers,
  getPositions,
  addTimeEntry,
  removeTimeEntry,
  updatePlayerStats,
  submitMatchStats,
  getMatchActivities,
  setTimeEntries,
  downloadMatchReport,
  getMatchStatsData,
  setMatchDuration,
} from "@/redux/slices/matchReducer";
import MatchHeader from "./sections/MatchHeader";
import ControlsSection from "./sections/ControlsSection";
import StatsTable from "./table/StatsTable";
import { ConfirmDialog, ErrorDialog, NoDataDialog, SuccessDialog } from "./Dialogs/Dialogs";
import "./../../../styles/components/MatchStatsTable.scss";
import "./../../../styles/components/EditableCells.scss";

interface MatchStatsTableProps {
  mode?: "edit" | "view";
  matchId?: string;
  opponentIds?: string;
  teamId?: string;
  teamName?: string;
}

interface Player {
  no?: string | number;
  name?: string;
  pos?: string;
  assists?: string | number | Array<{ id: number; minute: number }>;
  goals?: any;
  red?: string | number;
  yellow?: string | number;
  subOut?: string | number;
  subIn?: string | number;
  playingTime?: string | number;
  ratings?: string | number;
  givenPosition?: string;
  userId?: string;
  sportsProfileId?: string;
}

const MatchStatsTable = memo(({ mode = "edit", matchId, opponentIds, teamId, teamName: propTeamName }: MatchStatsTableProps) => {
  const dispatch = useDispatch();
  const router = useRouter();

  const {
    opponents,
    players,
    positions,
    timeEntries,
    matchActivities,
    isOpponentsLoading,
    isPlayersLoading,
    isPositionsLoading,
    selectedOpponent,
    isDownloadingReport,
    isLoadingMatchStats,
    matchDuration,
  } = useSelector((state: RootState) => state.match);

  const teamName = propTeamName || "Team";

  const { events, isEventLoading } = useSelector((state: RootState) => state?.event);

  const [newTimeEntry, setNewTimeEntry] = useState("");
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [showErrorDialog, setShowErrorDialog] = useState(false);
  const [showNoDataDialog, setShowNoDataDialog] = useState(false);
  const [showSuccessDialog, setShowSuccessDialog] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const [highlightedCells, setHighlightedCells] = useState<Set<string>>(new Set());
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [dataLoadAttempts, setDataLoadAttempts] = useState(0);
  const [hasValidationErrors, setHasValidationErrors] = useState(false);
  const [isMatchDurationValid, setIsMatchDurationValid] = useState(false);

  const calculateMatchDuration = useCallback(() => {
    return matchDuration || null;
  }, [matchDuration]);

  const handleMatchDurationChange = useCallback(
    (duration: number | null) => {
      dispatch(setMatchDuration(duration));
    },
    [dispatch]
  );

  const handleMatchDurationValidationChange = useCallback((isValid: boolean) => {
    setIsMatchDurationValid(isValid);
  }, []);

  useEffect(() => {
    return () => {
      dispatch(setTimeEntries([]));
    };
  }, [dispatch]);

  useEffect(() => {
    dispatch(getPositions() as any);
    dispatch(getMatchActivities() as any);

    if (opponentIds) {
      dispatch(getOpponents({ opponentIds, page: 1, size: 1 }) as any);
    }
    if (teamId) {
      dispatch(getPlayers({ teamId, page: 1, size: 1000, generateImageUrl: false }) as any);
    }
  }, [dispatch, opponentIds, teamId]);

  useEffect(() => {
    if (mode === "view" && matchId && matchActivities.length > 0 && players.length > 0) {
      dispatch(getMatchStatsData({ matchId }) as any);
    }
  }, [dispatch, mode, matchId, matchActivities.length, players.length]);

  useEffect(() => {
    if (mode === "view" && matchId && !isLoadingMatchStats && dataLoadAttempts < 3) {
      const shouldHaveData = players.length > 0;
      const hasStatsData = players.some(
        (player) =>
          (Array.isArray(player.assists) && player.assists.length > 0) ||
          (Array.isArray(player.goals) && player.goals.length > 0) ||
          player.red ||
          player.yellow ||
          player.subOut ||
          player.subIn ||
          player.playingTime ||
          player.ratings
      );

      if (shouldHaveData && !hasStatsData) {
        setDataLoadAttempts((prev) => prev + 1);
        dispatch(getMatchStatsData({ matchId }) as any);
      }
    }
  }, [mode, matchId, isLoadingMatchStats, players, dataLoadAttempts, dispatch]);

  const effectiveMode = mode;

  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (hasUnsavedChanges && effectiveMode === "edit" && !isSubmitted) {
        e.preventDefault();
        e.returnValue = "You have unsaved changes. Are you sure you want to leave?";
        return "You have unsaved changes. Are you sure you want to leave?";
      }
    };

    window.addEventListener("beforeunload", handleBeforeUnload);
    return () => window.removeEventListener("beforeunload", handleBeforeUnload);
  }, [hasUnsavedChanges, effectiveMode, isSubmitted]);

  const calculateTotalGoals = useCallback(() => {
    return players.reduce((total, player) => {
      if (Array.isArray(player.goals)) {
        return total + player.goals.length;
      }
      const playerGoals = Number(player.goals) || 0;
      return total + playerGoals;
    }, 0);
  }, [players]);

  const calculateTotalAssists = useCallback(() => {
    return players.reduce((total, player) => {
      if (Array.isArray(player.assists)) {
        return total + player.assists.length;
      }
      const playerAssists = Number(player.assists) || 0;
      return total + playerAssists;
    }, 0);
  }, [players]);

  useEffect(() => {
    const hasData =
      players.some(
        (player) =>
          (Array.isArray(player.assists) ? player.assists.length : player.assists) ||
          (Array.isArray(player.goals) ? player.goals.length : player.goals) ||
          player.red ||
          player.yellow ||
          player.subOut ||
          player.subIn ||
          player.playingTime ||
          player.ratings
      ) || timeEntries.length > 0;

    setHasUnsavedChanges(hasData);

    if (hasData) {
      const MATCH_DURATION = calculateMatchDuration();
      let hasErrors = false;
      const invalidCells = new Set<string>();

      if (MATCH_DURATION) {
        timeEntries.forEach((entry, index) => {
          if (entry.time > MATCH_DURATION) {
            hasErrors = true;
            invalidCells.add(`opponent-${index}`);
          }
        });

        players.forEach((player, playerIndex) => {
          if (Array.isArray(player.assists)) {
            player.assists.forEach((assist: any) => {
              if (assist.minute > MATCH_DURATION) {
                hasErrors = true;
                invalidCells.add(`player-${playerIndex}-assists`);
              }
            });
          }

          if (Array.isArray(player.goals)) {
            player.goals.forEach((goal: any) => {
              if (goal.minute > MATCH_DURATION) {
                hasErrors = true;
                invalidCells.add(`player-${playerIndex}-goals`);
              }
            });
          }

          if (player.subOut && Number(player.subOut) > MATCH_DURATION) {
            hasErrors = true;
            invalidCells.add(`player-${playerIndex}-subOut`);
          }
          if (player.subIn && Number(player.subIn) > MATCH_DURATION) {
            hasErrors = true;
            invalidCells.add(`player-${playerIndex}-subIn`);
          }
          if (player.playingTime && Number(player.playingTime) > MATCH_DURATION) {
            hasErrors = true;
            invalidCells.add(`player-${playerIndex}-playingTime`);
          }

          if (player.yellow) {
            const yellowValue = Number(player.yellow);
            if (!isNaN(yellowValue) && yellowValue > MATCH_DURATION) {
              hasErrors = true;
              invalidCells.add(`player-${playerIndex}-yellow`);
            }
          }

          if (player.red) {
            const redValue = Number(player.red);
            if (!isNaN(redValue) && redValue > MATCH_DURATION) {
              hasErrors = true;
              invalidCells.add(`player-${playerIndex}-red`);
            }
          }
        });
      }

      players.forEach((player, playerIndex) => {
        if (player.ratings) {
          const rating = Number(player.ratings);
          if (isNaN(rating) || rating < 1 || rating > 10) {
            hasErrors = true;
            invalidCells.add(`player-${playerIndex}-ratings`);
          }
        }
      });

      const totalGoals = calculateTotalGoals();
      const totalAssists = calculateTotalAssists();
      if (totalAssists > totalGoals) {
        hasErrors = true;
      }

      setHasValidationErrors(hasErrors);
      setHighlightedCells(invalidCells);
    } else {
      setHasValidationErrors(false);
      setHighlightedCells(new Set());
    }
  }, [players, timeEntries, calculateMatchDuration, calculateTotalGoals, calculateTotalAssists, matchDuration]);

  useEffect(() => {
    return () => {
      dispatch(setMatchDuration(null));
    };
  }, [dispatch]);

  const handleBackClick = () => {
    dispatch(setMatchDuration(null));
    router.back();
  };

  const handlePlayerUpdate = useCallback(
    (index: number, field: string, value: string | number | any[]) => {
      dispatch(updatePlayerStats({ playerIndex: index, field, value }));
    },
    [dispatch]
  );

  useEffect(() => {
    players.forEach((player, playerIndex) => {
      let calculatedPlayingTime: string | number = "";
      const currentMatchDuration = Number(matchDuration) || 0;
      const subOutMinute = Number(player.subOut) || 0;
      const subInMinute = Number(player.subIn) || 0;

      if (subInMinute > 0 && subOutMinute > 0) {
        calculatedPlayingTime = Math.abs(subOutMinute - subInMinute);
      } else if (subInMinute > 0 && subOutMinute === 0) {
        calculatedPlayingTime = currentMatchDuration > subInMinute ? currentMatchDuration - subInMinute : 0;
      } else if (subInMinute === 0 && subOutMinute > 0) {
        calculatedPlayingTime = subOutMinute;
      } else {
        calculatedPlayingTime = player.playingTime || 0;
      }

      if (Number(player.playingTime) !== Number(calculatedPlayingTime)) {
        dispatch(
          updatePlayerStats({
            playerIndex: playerIndex,
            field: "playingTime",
            value: calculatedPlayingTime,
          })
        );
      }
    });
  }, [dispatch, players, matchDuration]);

  const showValidationError = useCallback((message: string) => {
    setErrorMessage(message);
    setShowErrorDialog(true);
  }, []);

  const handleAddTimeEntry = () => {
    if (newTimeEntry && !isNaN(Number(newTimeEntry))) {
      const newEntry = {
        id: Date.now(),
        time: Number(newTimeEntry),
        type: "opponent",
      };
      dispatch(addTimeEntry(newEntry));
      setNewTimeEntry("");
    }
  };

  const scrollToFirstHighlightedCell = () => {
    if (highlightedCells.size > 0) {
      const firstCell = Array.from(highlightedCells)[0];

      const element = document.querySelector(`.highlight-error, [data-cell-id="${firstCell}"]`);
      if (element) {
        element.scrollIntoView({
          behavior: "smooth",
          block: "center",
        });

        (element as HTMLElement).focus?.();
      }
    }
  };

  const validateAllFields = useCallback(() => {
    const MATCH_DURATION = calculateMatchDuration();
    const invalidCells = new Set<string>();
    let hasErrors = false;
    let hasRatingErrors = false;
    let hasDurationErrors = false;
    let hasAssistsErrors = false;

    if (MATCH_DURATION) {
      timeEntries.forEach((entry, index) => {
        const timeValue = Number(entry.time);
        if (timeValue > MATCH_DURATION) {
          invalidCells.add(`opponent-${index}`);
          hasErrors = true;
          hasDurationErrors = true;
        }
      });

      players.forEach((player, playerIndex) => {
        if (Array.isArray(player.assists)) {
          player.assists.forEach((assist: any) => {
            const minute = Number(assist.minute);
            if (minute > MATCH_DURATION) {
              invalidCells.add(`player-${playerIndex}-assists`);
              hasErrors = true;
              hasDurationErrors = true;
            }
          });
        }

        if (Array.isArray(player.goals)) {
          player.goals.forEach((goal: any) => {
            const minute = Number(goal.minute);
            if (minute > MATCH_DURATION) {
              invalidCells.add(`player-${playerIndex}-goals`);
              hasErrors = true;
              hasDurationErrors = true;
            }
          });
        }

        if (player.subOut) {
          const subOutValue = Number(player.subOut);
          if (!isNaN(subOutValue) && subOutValue > MATCH_DURATION) {
            invalidCells.add(`player-${playerIndex}-subOut`);
            hasErrors = true;
            hasDurationErrors = true;
          }
        }

        if (player.subIn) {
          const subInValue = Number(player.subIn);
          if (!isNaN(subInValue) && subInValue > MATCH_DURATION) {
            invalidCells.add(`player-${playerIndex}-subIn`);
            hasErrors = true;
            hasDurationErrors = true;
          }
        }

        if (player.playingTime) {
          const playingTimeValue = Number(player.playingTime);
          if (!isNaN(playingTimeValue) && playingTimeValue > MATCH_DURATION) {
            invalidCells.add(`player-${playerIndex}-playingTime`);
            hasErrors = true;
            hasDurationErrors = true;
          }
        }

        if (player.yellow) {
          const yellowValue = Number(player.yellow);
          if (!isNaN(yellowValue) && yellowValue > MATCH_DURATION) {
            invalidCells.add(`player-${playerIndex}-yellow`);
            hasErrors = true;
            hasDurationErrors = true;
          }
        }

        if (player.red) {
          const redValue = Number(player.red);
          if (!isNaN(redValue) && redValue > MATCH_DURATION) {
            invalidCells.add(`player-${playerIndex}-red`);
            hasErrors = true;
            hasDurationErrors = true;
          }
        }
      });
    }

    players.forEach((player, playerIndex) => {
      if (player.ratings !== undefined && player.ratings !== null && player.ratings !== "") {
        const rating = Number(player.ratings);
        const isInvalidRating = isNaN(rating) || rating < 1 || rating > 10;
        if (isInvalidRating) {
          invalidCells.add(`player-${playerIndex}-ratings`);
          hasErrors = true;
          hasRatingErrors = true;
        }
      }
    });

    const totalGoals = calculateTotalGoals();
    const totalAssists = calculateTotalAssists();
    if (totalAssists > totalGoals) {
      hasErrors = true;
      hasAssistsErrors = true;
    }

    return { hasErrors, invalidCells, hasRatingErrors, hasDurationErrors, hasAssistsErrors };
  }, [calculateMatchDuration, players, timeEntries, calculateTotalGoals, calculateTotalAssists]);

  const hasAnyData = () => {
    return (
      players.some(
        (player) =>
          (Array.isArray(player.assists) ? player.assists.length : player.assists) ||
          (Array.isArray(player.goals) ? player.goals.length : player.goals) ||
          player.red ||
          player.yellow ||
          player.subOut ||
          player.subIn ||
          player.playingTime ||
          player.ratings
      ) || timeEntries.length > 0
    );
  };

  const handleSubmit = () => {
    if (!matchDuration) {
      setErrorMessage("Please enter a match duration before submitting.");
      setShowErrorDialog(true);
      return;
    }

    const validationResult = validateAllFields();

    if (hasValidationErrors || validationResult.hasErrors) {
      let errorMessage = "";
      const errorCount = [validationResult.hasRatingErrors, validationResult.hasDurationErrors, validationResult.hasAssistsErrors].filter(Boolean).length;

      if (errorCount > 1) {
        let errors = [];
        let errorNum = 1;
        if (validationResult.hasRatingErrors) {
          errors.push(`${errorNum}. Match ratings must be between 1 and 10. Please enter a value within this range.`);
          errorNum++;
        }
        if (validationResult.hasDurationErrors) {
          errors.push(
            `${errorNum}. You have entered timestamps that exceed the match duration or the commas may not have been entered correctly between timestamps. Please review and correct your input.`
          );
          errorNum++;
        }
        if (validationResult.hasAssistsErrors) {
          errors.push(`${errorNum}. The number of assists cannot exceed the number of goals.`);
        }
        errorMessage = "There are multiple validation errors:\n\n" + errors.join("\n\n");
      } else if (validationResult.hasRatingErrors) {
        errorMessage = "Match ratings must be between 1 and 10. Please enter a value within this range.";
      } else if (validationResult.hasDurationErrors) {
        errorMessage =
          "You have entered timestamps that exceed the match duration or the commas may not have been entered correctly between timestamps. Please review and correct your input.";
      } else if (validationResult.hasAssistsErrors) {
        errorMessage = "The number of assists cannot exceed the number of goals.";
      } else {
        errorMessage =
          "Please fix all validation errors before submitting. Check that all ratings are between 1-10, timestamps don't exceed match duration, and assists don't exceed goals.";
      }

      setErrorMessage(errorMessage);
      setHighlightedCells(validationResult.invalidCells);
      setShowErrorDialog(true);
      setHasValidationErrors(true);
      return;
    }

    if (!matchActivities || matchActivities.length === 0) {
      setErrorMessage("Match activities are still loading. Please wait and try again.");
      setShowErrorDialog(true);
      return;
    }

    if (!hasAnyData()) {
      setErrorMessage("No data entered. Please add data before submitting");
      setShowNoDataDialog(true);
      return;
    }

    const { hasErrors, invalidCells, hasRatingErrors, hasDurationErrors, hasAssistsErrors } = validateAllFields();

    if (hasErrors) {
      let errorMessage = "";
      const errorCount = [hasRatingErrors, hasDurationErrors, hasAssistsErrors].filter(Boolean).length;

      if (errorCount > 1) {
        let errors = [];
        let errorNum = 1;
        if (hasRatingErrors) {
          errors.push(`${errorNum}. Match ratings must be between 1 and 10. Please enter a value within this range.`);
          errorNum++;
        }
        if (hasDurationErrors) {
          errors.push(
            `${errorNum}. You have entered timestamps that exceed the match duration or the commas may not have been entered correctly between timestamps. Please review and correct your input.`
          );
          errorNum++;
        }
        if (hasAssistsErrors) {
          errors.push(`${errorNum}. The number of assists cannot exceed the number of goals.`);
        }
        errorMessage = "There are multiple validation errors:\n\n" + errors.join("\n\n");
      } else if (hasRatingErrors) {
        errorMessage = "Match ratings must be between 1 and 10. Please enter a value within this range.";
      } else if (hasDurationErrors) {
        errorMessage =
          "You have entered timestamps that exceed the match duration or the commas may not have been entered correctly between timestamps. Please review and correct your input.";
      } else if (hasAssistsErrors) {
        errorMessage = "The number of assists cannot exceed the number of goals.";
      }

      setErrorMessage(errorMessage);
      setHighlightedCells(invalidCells);
      setShowErrorDialog(true);
      setHasValidationErrors(true);
      return;
    }

    setShowConfirmDialog(true);
  };

  const getActivityIdByCode = useCallback(
    (code: string) => {
      const activity = matchActivities.find((activity) => activity.code === code);
      return activity?._id || "";
    },
    [matchActivities]
  );

  const handleSuccessDialogClose = () => {
    setShowSuccessDialog(false);
    dispatch(setMatchDuration(null));
    router.back();
  };

  const handleDownload = async () => {
    if (isDownloadingReport) return;

    if (!teamId || !matchId) {
      setErrorMessage("Missing required parameters for download.");
      setShowErrorDialog(true);
      return;
    }

    try {
      await dispatch(
        downloadMatchReport({
          teamId,
          matchId,
          teamName: teamName || "Team",
          opponentName: opponents?.[0]?.name || "Opponent",
        }) as any
      );
    } catch (error: any) {
      setErrorMessage(error?.message || "Failed to download report. Please try again.");
      setShowErrorDialog(true);
    }
  };

  const handleConfirmedSubmit = async () => {
    console.log("=== CONFIRMED SUBMIT VALIDATION ===");

    const { hasErrors, invalidCells, hasRatingErrors, hasDurationErrors, hasAssistsErrors } = validateAllFields();
    console.log("Final validation check:", { hasErrors, hasRatingErrors, hasDurationErrors, hasAssistsErrors });

    if (hasErrors) {
      console.log("CRITICAL: Submission blocked at final validation");
      setIsSubmitting(false);
      setShowConfirmDialog(false);

      let errorMessage = "";
      if (hasRatingErrors && hasDurationErrors) {
        errorMessage =
          "There are multiple validation errors:\n\n" +
          "1. Match ratings must be between 1 and 10. Please enter a value within this range.\n\n" +
          "2. You have entered timestamps that exceed the match duration or the commas may not have been entered correctly between timestamps. Please review and correct your input.";
      } else if (hasRatingErrors) {
        errorMessage = "Match ratings must be between 1 and 10. Please enter a value within this range.";
      } else if (hasDurationErrors) {
        errorMessage =
          "You have entered timestamps that exceed the match duration or the commas may not have been entered correctly between timestamps. Please review and correct your input.";
      }

      setErrorMessage(errorMessage);
      setHighlightedCells(invalidCells);
      setShowErrorDialog(true);
      return;
    }

    console.log("Final validation passed, proceeding with submission");
    setIsSubmitting(true);

    try {
      const playerMatchActions = players.map((player) => {
        const activitiesMap = new Map<string, number[]>();

        if (Array.isArray(player.assists)) {
          const assistActivityId = getActivityIdByCode("ASSIST");
          if (assistActivityId) {
            const assistMinutes = player.assists.map((assist: any) => assist.minute);
            if (assistMinutes.length > 0) {
              activitiesMap.set(assistActivityId, assistMinutes);
            }
          }
        }

        if (Array.isArray(player.goals)) {
          const goalActivityId = getActivityIdByCode("GOAL_SCORED");
          if (goalActivityId) {
            const goalMinutes = player.goals.map((goal: any) => goal.minute);
            if (goalMinutes.length > 0) {
              activitiesMap.set(goalActivityId, goalMinutes);
            }
          }
        }

        if (player.red && Number(player.red) > 0) {
          const redCardActivityId = getActivityIdByCode("RED_CARD");
          if (redCardActivityId) {
            activitiesMap.set(redCardActivityId, [Number(player.red)]);
          }
        }

        if (player.yellow && Number(player.yellow) > 0) {
          const yellowCardActivityId = getActivityIdByCode("YELLOW_CARD");
          if (yellowCardActivityId) {
            activitiesMap.set(yellowCardActivityId, [Number(player.yellow)]);
          }
        }

        if (player.subOut && Number(player.subOut) > 0) {
          const subOutActivityId = getActivityIdByCode("SUB_OUT");
          if (subOutActivityId) {
            activitiesMap.set(subOutActivityId, [Number(player.subOut)]);
          }
        }

        if (player.subIn && Number(player.subIn) > 0) {
          const subInActivityId = getActivityIdByCode("SUB_IN");
          if (subInActivityId) {
            activitiesMap.set(subInActivityId, [Number(player.subIn)]);
          }
        }

        const matchActivities = Array.from(activitiesMap.entries()).map(([activityId, minutes]) => ({
          activityId,
          minutes,
        }));

        return {
          userId: player.userId || "",
          sportsProfileId: player.sportsProfileId || "",
          position: player.pos || "",
          jerseyNo: player.no?.toString() || "",
          rating: player.ratings?.toString() || "",
          playingTime: player.playingTime?.toString() || "",
          matchActivities,
        };
      });

      const requestBody = {
        matchId: matchId || "",
        opponentScoreTimes: timeEntries.map((entry) => entry.time),
        concludedTime: new Date().toISOString(),
        playerMatchActions,
      };

      await dispatch(submitMatchStats(requestBody) as any);

      setShowConfirmDialog(false);
      setHasUnsavedChanges(false);
      setIsSubmitted(true);

      dispatch(setTimeEntries([]));

      setShowSuccessDialog(true);
    } catch (error) {
      setErrorMessage("Failed to submit match stats. Please try again.");
      setShowErrorDialog(true);
      setShowConfirmDialog(false);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleRemoveTimeEntry = useCallback(
    (id: number) => {
      dispatch(removeTimeEntry(id));
    },
    [dispatch]
  );

  if (!opponentIds || !teamId) {
    return (
      <div className="error-container">
        <div className="message">
          <p>Missing required parameters.</p>
          <p>Please navigate from the match dashboard.</p>
        </div>
      </div>
    );
  }

  if (isPlayersLoading || isOpponentsLoading || isPositionsLoading || isLoadingMatchStats) {
    return (
      <div className="loading-container">
        <div className="message">Loading match data...</div>
      </div>
    );
  }

  const hasMatchData = hasAnyData();

  if (effectiveMode === "view" && !hasMatchData && !isLoadingMatchStats) {
    return (
      <div className="match-stats-container">
        <MatchHeader
          teamName={teamName}
          totalGoals={calculateTotalGoals()}
          timeEntriesLength={timeEntries.length}
          opponentName={opponents?.[0]?.name || ""}
          effectiveMode={effectiveMode}
          isSubmitted={isSubmitted}
          isSubmitting={isSubmitting}
          isDownloading={isDownloadingReport}
          hasData={false}
          handleSubmit={handleSubmit}
          handleDownload={handleDownload}
        />

        <ControlsSection
          handleBackClick={handleBackClick}
          effectiveMode={effectiveMode}
          isSubmitted={isSubmitted}
          newTimeEntry={newTimeEntry}
          setNewTimeEntry={setNewTimeEntry}
          isSubmitting={isSubmitting}
          handleAddTimeEntry={handleAddTimeEntry}
          timeEntries={timeEntries}
          handleRemoveTimeEntry={handleRemoveTimeEntry}
          events={events}
          showValidationError={showValidationError}
          matchId={matchId}
          matchDuration={matchDuration}
          onMatchDurationChange={handleMatchDurationChange}
          onMatchDurationValidationChange={handleMatchDurationValidationChange}
        />

        <div className="no-data-container">
          <div className="no-data-message">
            <h3>No available reports</h3>
            <p>There are no match statistics available for this match.</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="match-stats-container">
      <MatchHeader
        teamName={teamName}
        totalGoals={calculateTotalGoals()}
        timeEntriesLength={timeEntries.length}
        opponentName={opponents?.[0]?.name || ""}
        effectiveMode={effectiveMode}
        isSubmitted={isSubmitted}
        isSubmitting={isSubmitting}
        isDownloading={isDownloadingReport}
        hasData={hasMatchData}
        handleSubmit={handleSubmit}
        handleDownload={handleDownload}
      />

      <ControlsSection
        handleBackClick={handleBackClick}
        effectiveMode={effectiveMode}
        isSubmitted={isSubmitted}
        newTimeEntry={newTimeEntry}
        setNewTimeEntry={setNewTimeEntry}
        isSubmitting={isSubmitting}
        handleAddTimeEntry={handleAddTimeEntry}
        timeEntries={timeEntries}
        handleRemoveTimeEntry={handleRemoveTimeEntry}
        events={events}
        showValidationError={showValidationError}
        matchId={matchId}
        matchDuration={matchDuration}
        onMatchDurationChange={handleMatchDurationChange}
        onMatchDurationValidationChange={handleMatchDurationValidationChange}
      />

      <StatsTable
        players={players}
        positions={positions}
        effectiveMode={effectiveMode}
        highlightedCells={highlightedCells}
        handlePlayerUpdate={handlePlayerUpdate}
        events={events}
        showValidationError={showValidationError}
        matchId={matchId || ""}
        matchDuration={matchDuration}
        isMatchDurationValid={isMatchDurationValid}
      />

      <ConfirmDialog isOpen={showConfirmDialog} onConfirm={handleConfirmedSubmit} onCancel={() => setShowConfirmDialog(false)} />

      <ErrorDialog isOpen={showErrorDialog} message={errorMessage} onClose={() => setShowErrorDialog(false)} onScrollToError={scrollToFirstHighlightedCell} />

      <NoDataDialog isOpen={showNoDataDialog} message={errorMessage} onClose={() => setShowNoDataDialog(false)} />

      <SuccessDialog isOpen={showSuccessDialog} onClose={handleSuccessDialogClose} />
    </div>
  );
});

MatchStatsTable.displayName = "MatchStatsTable";

export default MatchStatsTable;
