import { memo } from "react";

interface DialogProps {
  isOpen: boolean;
  onClose: () => void;
  children: React.ReactNode;
}

const Dialog = memo(({ isOpen, onClose, children }: DialogProps) => {
  if (!isOpen) return null;

  return (
    <div className="dialog-overlay">
      <div className="dialog-box">{children}</div>
    </div>
  );
});

Dialog.displayName = "Dialog";

interface ConfirmDialogProps {
  isOpen: boolean;
  onConfirm: () => void;
  onCancel: () => void;
}

export const ConfirmDialog = memo(({ isOpen, onConfirm, onCancel }: ConfirmDialogProps) => {
  return (
    <Dialog isOpen={isOpen} onClose={onCancel}>
      <div className="dialog-icon warning">!</div>
      <h3>Are you sure you want to submit?</h3>
      <p>Once submitted, the stats cannot be edited</p>
      <div className="dialog-buttons">
        <button className="dialog-btn dialog-btn--yes" onClick={onConfirm}>
          Yes
        </button>
        <button className="dialog-btn dialog-btn--no" onClick={onCancel}>
          No
        </button>
      </div>
    </Dialog>
  );
});

ConfirmDialog.displayName = "ConfirmDialog";

interface ErrorDialogProps {
  isOpen: boolean;
  message: string;
  onClose: () => void;
  onScrollToError?: () => void;
}

export const ErrorDialog = memo(({ isOpen, message, onClose, onScrollToError }: ErrorDialogProps) => {
  const handleClose = () => {
    onClose();
    if (onScrollToError) {
      onScrollToError();
    }
  };

  return (
    <Dialog isOpen={isOpen} onClose={onClose}>
      <div className="dialog-icon error">!</div>
      <h3>Validation Error</h3>
      <p style={{ whiteSpace: "pre-line" }}>{message}</p>
      <div className="dialog-buttons">
        <button className="dialog-btn dialog-btn--ok" onClick={handleClose}>
          OK
        </button>
      </div>
    </Dialog>
  );
});

ErrorDialog.displayName = "ErrorDialog";

interface NoDataDialogProps {
  isOpen: boolean;
  message: string;
  onClose: () => void;
}

export const NoDataDialog = memo(({ isOpen, message, onClose }: NoDataDialogProps) => {
  return (
    <Dialog isOpen={isOpen} onClose={onClose}>
      <div className="dialog-icon warning">!</div>
      <h3>No Data Entered</h3>
      <p>{message}</p>
      <div className="dialog-buttons">
        <button className="dialog-btn dialog-btn--ok" onClick={onClose}>
          OK
        </button>
      </div>
    </Dialog>
  );
});

NoDataDialog.displayName = "NoDataDialog";

interface SuccessDialogProps {
  isOpen: boolean;
  onClose: () => void;
}

export const SuccessDialog = memo(({ isOpen, onClose }: SuccessDialogProps) => {
  return (
    <Dialog isOpen={isOpen} onClose={onClose}>
      <div className="dialog-icon success">✓</div>
      <h3>Match Stats Successfully Updated</h3>
      <p className="success-subtitle">Match stats successfully synced with the Koach Hub app.</p>
      <div className="dialog-buttons">
        <button className="dialog-btn dialog-btn--continue" onClick={onClose}>
          Continue
        </button>
      </div>
    </Dialog>
  );
});

SuccessDialog.displayName = "SuccessDialog";

export default Dialog;
