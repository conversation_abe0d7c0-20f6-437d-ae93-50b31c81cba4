import { memo, useCallback, useState, useEffect } from "react";

interface TimeEntry {
  id: number;
  time: number;
  type: string;
}

interface ControlsSectionProps {
  handleBackClick: () => void;
  effectiveMode: string;
  isSubmitted: boolean;
  newTimeEntry: string;
  setNewTimeEntry: (value: string) => void;
  isSubmitting: boolean;
  handleAddTimeEntry: () => void;
  timeEntries: TimeEntry[];
  handleRemoveTimeEntry: (id: number) => void;
  events?: any[];
  showValidationError?: (message: string) => void;
  matchId?: string;
  matchDuration?: number | null;
  onMatchDurationChange?: (duration: number | null) => void;
}

const ControlsSection = memo(
  ({
    handleBackClick,
    effectiveMode,
    isSubmitted,
    newTimeEntry,
    setNewTimeEntry,
    isSubmitting,
    handleAddTimeEntry,
    timeEntries,
    handleRemoveTimeEntry,
    events,
    showValidationError,
    matchId,
    matchDuration,
    onMatchDurationChange,
  }: ControlsSectionProps) => {
    const [isInvalid, setIsInvalid] = useState(false);
    const [durationInputValue, setDurationInputValue] = useState(matchDuration?.toString() || "");
    const [isDurationInvalid, setIsDurationInvalid] = useState(false);

    useEffect(() => {
      setDurationInputValue(matchDuration?.toString() || "");
    }, [matchDuration]);

    const calculateMatchDuration = useCallback(() => {
      return matchDuration || null;
    }, [matchDuration]);

    const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
      const invalidKeys = ["-", "+", "e", "E", "."];
      if (invalidKeys.includes(e.key)) {
        e.preventDefault();
      }
    };

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      let value = e.target.value;

      const numericValue = value.replace(/[^0-9]/g, "");
      if (numericValue !== value) {
        e.target.value = numericValue;
        value = numericValue;
      }

      setNewTimeEntry(value);

      if (numericValue !== "") {
        const timeValue = parseInt(numericValue);
        const matchDuration = calculateMatchDuration();

        if (matchDuration && !isNaN(timeValue) && timeValue > matchDuration) {
          setIsInvalid(true);
          if (showValidationError) {
            showValidationError("You have entered timestamps that exceed the match duration. Please review and correct.");
          }
        } else {
          setIsInvalid(false);
        }
      } else {
        setIsInvalid(false);
      }
    };

    const handleAddClick = () => {
      if (!newTimeEntry || newTimeEntry === "") {
        return;
      }

      const timeValue = parseInt(newTimeEntry);
      if (isNaN(timeValue)) {
        return;
      }

      const matchDuration = calculateMatchDuration();
      if (matchDuration && timeValue > matchDuration) {
        setIsInvalid(true);
        if (showValidationError) {
          showValidationError("You have entered timestamps that exceed the match duration.");
        }
        return;
      }

      setIsInvalid(false);
      handleAddTimeEntry();
    };

    const handleDurationInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      let value = e.target.value;

      const numericValue = value.replace(/[^0-9]/g, "");
      if (numericValue !== value) {
        e.target.value = numericValue;
        value = numericValue;
      }

      setDurationInputValue(value);

      if (value === "") {
        onMatchDurationChange?.(null);
        setIsDurationInvalid(false);
      } else {
        const duration = parseInt(value);
        if (!isNaN(duration) && duration > 0 && duration <= 120) {
          onMatchDurationChange?.(duration);
          setIsDurationInvalid(false);
        } else {
          setIsDurationInvalid(true);
        }
      }
    };

    const handleDurationKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
      const invalidKeys = ["-", "+", "e", "E", "."];
      if (invalidKeys.includes(e.key)) {
        e.preventDefault();
      }
    };

    const handleDurationBlur = () => {
      // Don't reset the input value - let user see their invalid input and correct it
      // Only reset if the field is completely empty
      if (durationInputValue === "") {
        setDurationInputValue(matchDuration?.toString() || "");
        setIsDurationInvalid(false);
      }
    };
    return (
      <div className="controls-section">
        <div className="controls-section__left">
          <button className="back-btn" onClick={handleBackClick}>
            ← Back
          </button>

          {effectiveMode === "edit" && (
            <div className="match-duration-input">
              <span className="match-duration-input__label">Match Duration *</span>
              <div style={{ position: "relative" }}>
                <div className={`match-duration-input__container ${isDurationInvalid ? "match-duration-input__container--invalid" : ""}`}>
                  <input
                    type="text"
                    placeholder="00"
                    value={durationInputValue}
                    onChange={handleDurationInputChange}
                    onKeyDown={handleDurationKeyDown}
                    onBlur={handleDurationBlur}
                    className="match-duration-input__field"
                    disabled={isSubmitted || isSubmitting}
                    min="1"
                    max="120"
                  />
                  <span className="match-duration-input__unit">min</span>
                </div>
                {isDurationInvalid && <span className="match-duration-input__error">Please enter a valid duration between 1 and 120 minutes</span>}
              </div>
            </div>
          )}
        </div>

        <div className="opponent-score">
          <span className="opponent-score__label">Opponent Score</span>
          <div className="opponent-score__entries">
            {effectiveMode === "edit" && !isSubmitted && (
              <div className={`opponent-score__add-section ${isInvalid ? "opponent-score__add-section--invalid" : ""}`}>
                <input
                  type="text"
                  placeholder="Time"
                  value={newTimeEntry}
                  onChange={handleInputChange}
                  onKeyDown={handleKeyDown}
                  className="time-input"
                  disabled={isSubmitting || !matchDuration}
                  max={calculateMatchDuration() || undefined}
                />
                <div onClick={matchDuration ? handleAddClick : undefined} className={`add-btn ${!matchDuration ? "add-btn--disabled" : ""}`}>
                  +
                </div>
              </div>
            )}
            {timeEntries.map((entry) => (
              <div key={entry.id} className="opponent-score__entry">
                <span className="time-text">{entry.time} min</span>
                {effectiveMode === "edit" && !isSubmitted && (
                  <div onClick={() => handleRemoveTimeEntry(entry.id)} className="remove-btn">
                    ✕
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }
);

ControlsSection.displayName = "ControlsSection";

export default ControlsSection;
