import { memo } from "react";
import Image from "next/image";

interface MatchHeaderProps {
  teamName: string;
  totalGoals: number;
  timeEntriesLength: number;
  opponentName: string;
  effectiveMode: string;
  isSubmitted: boolean;
  isSubmitting: boolean;
  isDownloading?: boolean;
  hasData?: boolean;
  handleSubmit: () => void;
  handleDownload: () => void;
}

const MatchHeader = memo(
  ({
    teamName,
    totalGoals,
    timeEntriesLength,
    opponentName,
    effectiveMode,
    isSubmitted,
    isSubmitting,
    isDownloading = false,
    hasData = true,
    handleSubmit,
    handleDownload,
  }: MatchHeaderProps) => {
    return (
      <div className="match-header">
        <div className="match-header__logo-section">
          <Image src="/images/koach_logo.png" alt="Koach" width={150} height={60} className="logo-image" priority />
        </div>

        <div className="match-header__score-section">
          <span className="team-name">{teamName}</span>
          <div className="score-home">{totalGoals.toString().padStart(1, "0")}</div>
          <span className="vs-text">VS</span>
          <div className="score-away">{timeEntriesLength}</div>
          <span className="opponent-name">{opponentName || ""}</span>
        </div>

        {effectiveMode === "edit" && !isSubmitted && (
          <button className="match-header__submit-btn" onClick={handleSubmit} disabled={isSubmitting}>
            {isSubmitting ? "Submitting..." : "Submit"}
          </button>
        )}
        {effectiveMode === "view" && (
          <button className="match-header__download-btn" onClick={handleDownload} disabled={isDownloading || !hasData}>
            {isDownloading ? "Downloading..." : "Download Report"}
          </button>
        )}
        {isSubmitted && <div className="match-header__submitted-status">✓ Submitted</div>}
      </div>
    );
  }
);

MatchHeader.displayName = "MatchHeader";

export default MatchHeader;
