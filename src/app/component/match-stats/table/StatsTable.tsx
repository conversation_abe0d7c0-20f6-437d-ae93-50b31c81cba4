import { memo, useState, useMemo } from "react";
import Image from "next/image";
import EditableCell from "./cells/EditableCell";
import GoalsCell from "./cells/GoalsCell";
import AssistsCell from "./cells/AssistsCell";
import PositionDropdown from "./cells/PositionDropdown";

interface Player {
  no?: string | number;
  name?: string;
  pos?: string;
  assists?: string | number | Array<{ id: number; minute: number }>;
  goals?: any;
  red?: string | number;
  yellow?: string | number;
  subOut?: string | number;
  subIn?: string | number;
  playingTime?: string | number;
  ratings?: string | number;
  givenPosition?: string;
}

interface Position {
  _id: string;
  name: string;
  shortName?: string;
}

interface StatsTableProps {
  players: Player[];
  positions: Position[];
  effectiveMode: string;
  highlightedCells: Set<string>;
  handlePlayerUpdate: (index: number, field: string, value: string | number | any[]) => void;
  events: any[];
  showValidationError: (message: string) => void;
  matchId: string;
  matchDuration: number | null;
  isMatchDurationValid?: boolean;
}

const StatsTable = memo(
  ({
    players,
    positions,
    effectiveMode,
    highlightedCells,
    handlePlayerUpdate,
    events,
    showValidationError,
    matchId,
    matchDuration,
    isMatchDurationValid = true,
  }: StatsTableProps) => {
    const isDisabled = effectiveMode === "edit" && (!matchDuration || !isMatchDurationValid);
    const [searchTerm, setSearchTerm] = useState("");
    const [showSearchInput, setShowSearchInput] = useState(false);

    const filteredPlayers = useMemo(() => {
      if (!searchTerm.trim()) {
        return players;
      }

      const searchLower = searchTerm.toLowerCase();

      return players.filter((player) => {
        if (!player.name) return false;

        const playerNameLower = player.name.toLowerCase();
        const words = playerNameLower.split(/\s+/);

        return words.some((word) => word.startsWith(searchLower));
      });
    }, [players, searchTerm]);

    const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      setSearchTerm(e.target.value);
    };

    const handleSearchIconClick = () => {
      setShowSearchInput(!showSearchInput);
      if (showSearchInput) {
        setSearchTerm("");
      }
    };

    return (
      <div className={`stats-table ${isDisabled ? "stats-table--disabled" : ""}`}>
        <table>
          <thead className="stats-table__header">
            <tr>
              <th className="text-left">No</th>
              <th className="text-left">Pos</th>
              <th className="text-left">
                <div className="name-header-with-search">
                  {showSearchInput ? (
                    <input
                      type="text"
                      placeholder="Search players..."
                      value={searchTerm}
                      onChange={handleSearchChange}
                      className="player-search-input"
                      autoFocus
                      onBlur={() => {
                        if (!searchTerm) {
                          setShowSearchInput(false);
                        }
                      }}
                    />
                  ) : (
                    <div className="name-header-top">
                      <span>Name</span>
                      <svg
                        width="16"
                        height="16"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className="search-icon"
                        onClick={handleSearchIconClick}
                      >
                        <circle cx="11" cy="11" r="8"></circle>
                        <path d="m21 21-4.35-4.35"></path>
                      </svg>
                    </div>
                  )}
                </div>
              </th>
              <th className="text-center">
                <div className="header-with-icon">
                  <Image src="/images/shoe_icon.png" alt="Assists" width={16} height={16} />
                  <span>Assists</span>
                </div>
              </th>
              <th className="text-center">
                <div className="header-with-icon">
                  <Image src="/images/ball_icon.png" alt="Goals" width={16} height={16} />
                  <span>Goals</span>
                </div>
              </th>
              <th className="text-center">
                <div className="header-with-icon">
                  <Image src="/images/red_card_icon.png" alt="Red Cards" width={16} height={16} />
                  <span>Red</span>
                </div>
              </th>
              <th className="text-center">
                <div className="header-with-icon">
                  <Image src="/images/yellow_card_icon.png" alt="Yellow Cards" width={16} height={16} />
                  <span>Yellow</span>
                </div>
              </th>
              <th className="text-center">
                <div className="header-with-icon">
                  <Image src="/images/arrow_down_icon.png" alt="Sub Out" width={16} height={16} />
                  <span>Sub Out</span>
                </div>
              </th>
              <th className="text-center">
                <div className="header-with-icon">
                  <Image src="/images/arrow_up_icon.png" alt="Sub In" width={16} height={16} />
                  <span>Sub In</span>
                </div>
              </th>
              <th className="text-center">
                <div className="header-with-icon">
                  <Image src="/images/clock_icon.png" alt="Playing Time" width={16} height={16} />
                  <span>Playing Time</span>
                </div>
              </th>
              <th className="text-center">Rating (1-10)</th>
            </tr>
          </thead>
          <tbody className="stats-table__body">
            {filteredPlayers.map((player, filteredIndex) => {
              const originalIndex = players.findIndex((p) => p.name === player.name && p.no === player.no);
              const playerIndex = originalIndex !== -1 ? originalIndex : filteredIndex;
              return (
                <tr key={filteredIndex} className={`${(player as any).hasStats ? "has-stats" : ""}`}>
                  <td>
                    <EditableCell
                      value={player.no}
                      playerIndex={playerIndex}
                      field="no"
                      type="text"
                      mode={effectiveMode}
                      handlePlayerUpdate={handlePlayerUpdate}
                      matchDuration={matchDuration}
                    />
                  </td>
                  <td className="position">
                    <PositionDropdown
                      value={player.pos}
                      playerIndex={playerIndex}
                      mode={effectiveMode}
                      players={players}
                      positions={positions}
                      handlePlayerUpdate={handlePlayerUpdate}
                    />
                  </td>
                  <td className="player-name">{player.name || ""}</td>
                  <td className={`text-center ${highlightedCells.has(`player-${playerIndex}-assists`) ? "highlight-error" : ""}`}>
                    <AssistsCell
                      player={player}
                      playerIndex={playerIndex}
                      mode={effectiveMode}
                      handlePlayerUpdate={handlePlayerUpdate}
                      events={events}
                      showValidationError={showValidationError}
                      matchId={matchId}
                      matchDuration={matchDuration}
                    />
                  </td>
                  <td className={`text-center ${highlightedCells.has(`player-${playerIndex}-goals`) ? "highlight-error" : ""}`}>
                    <GoalsCell
                      player={player}
                      playerIndex={playerIndex}
                      mode={effectiveMode}
                      handlePlayerUpdate={handlePlayerUpdate}
                      events={events}
                      showValidationError={showValidationError}
                      matchId={matchId}
                      matchDuration={matchDuration}
                    />
                  </td>
                  <td className="text-center">
                    <EditableCell
                      value={player.red}
                      playerIndex={playerIndex}
                      field="red"
                      type="number"
                      mode={effectiveMode}
                      handlePlayerUpdate={handlePlayerUpdate}
                      events={events}
                      showValidationError={showValidationError}
                      matchId={matchId}
                      matchDuration={matchDuration}
                    />
                  </td>
                  <td className="text-center">
                    <EditableCell
                      value={player.yellow}
                      playerIndex={playerIndex}
                      field="yellow"
                      type="number"
                      mode={effectiveMode}
                      handlePlayerUpdate={handlePlayerUpdate}
                      events={events}
                      showValidationError={showValidationError}
                      matchId={matchId}
                      matchDuration={matchDuration}
                    />
                  </td>
                  <td className={`text-center ${highlightedCells.has(`player-${playerIndex}-subOut`) ? "highlight-error" : ""}`}>
                    <EditableCell
                      value={player.subOut}
                      playerIndex={playerIndex}
                      field="subOut"
                      type="number"
                      mode={effectiveMode}
                      handlePlayerUpdate={handlePlayerUpdate}
                      events={events}
                      showValidationError={showValidationError}
                      matchId={matchId}
                      matchDuration={matchDuration}
                    />
                  </td>
                  <td className={`text-center ${highlightedCells.has(`player-${playerIndex}-subIn`) ? "highlight-error" : ""}`}>
                    <EditableCell
                      value={player.subIn}
                      playerIndex={playerIndex}
                      field="subIn"
                      type="number"
                      mode={effectiveMode}
                      handlePlayerUpdate={handlePlayerUpdate}
                      events={events}
                      showValidationError={showValidationError}
                      matchId={matchId}
                      matchDuration={matchDuration}
                    />
                  </td>
                  <td className={`text-center ${highlightedCells.has(`player-${playerIndex}-playingTime`) ? "highlight-error" : ""}`}>
                    <EditableCell
                      value={player.playingTime}
                      playerIndex={playerIndex}
                      field="playingTime"
                      type="number"
                      mode={effectiveMode}
                      handlePlayerUpdate={handlePlayerUpdate}
                      events={events}
                      showValidationError={showValidationError}
                      matchId={matchId}
                      matchDuration={matchDuration}
                    />
                  </td>
                  <td className="text-center rating">
                    <EditableCell
                      value={player.ratings}
                      playerIndex={playerIndex}
                      field="ratings"
                      type="number"
                      mode={effectiveMode}
                      handlePlayerUpdate={handlePlayerUpdate}
                      matchDuration={matchDuration}
                    />
                  </td>
                </tr>
              );
            })}
          </tbody>
        </table>
        {isDisabled && (
          <div className="stats-table__disabled-overlay">
            <div className="stats-table__disabled-message">Please set the match duration before entering match statistics</div>
          </div>
        )}
      </div>
    );
  }
);

StatsTable.displayName = "StatsTable";

export default StatsTable;
