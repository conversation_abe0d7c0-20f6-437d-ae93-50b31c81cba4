import { memo, useState, useCallback, useMemo, useEffect } from "react";

interface Player {
  goals?: any;
}

interface GoalsCellProps {
  player: Player;
  playerIndex: number;
  mode: string;
  handlePlayerUpdate: (index: number, field: string, value: any[]) => void;
  events?: any[];
  showValidationError?: (message: string) => void;
  matchId?: string;
  matchDuration?: number | null;
}

const GoalsCell = memo(({ player, playerIndex, mode, handlePlayerUpdate, events, showValidationError, matchId, matchDuration }: GoalsCellProps) => {
  const [inputValue, setInputValue] = useState("");
  const [isTyping, setIsTyping] = useState(false);
  const [isInvalid, setIsInvalid] = useState(false);

  const calculateMatchDuration = useCallback(() => {
    return matchDuration || null;
  }, [matchDuration]);

  const goalsToString = useCallback((goals: any) => {
    if (Array.isArray(goals)) {
      return goals
        .map((goal: any) => goal.minute)
        .sort((a: number, b: number) => a - b)
        .join(", ");
    }
    return "";
  }, []);

  const stringToGoals = useCallback(
    (str: string) => {
      if (!str || !str.trim()) {
        setIsInvalid(false);
        return [];
      }

      const goalMinutes = str
        .split(",")
        .map((minute) => minute.trim())
        .filter((minute) => minute !== "" && !isNaN(Number(minute)) && Number(minute) > 0)
        .map((minute) => Number(minute));

      const matchDuration = calculateMatchDuration();
      if (matchDuration) {
        const invalidMinutes = goalMinutes.filter((minute) => minute > matchDuration);
        if (invalidMinutes.length > 0) {
          setIsInvalid(true);
          if (showValidationError) {
            showValidationError("You have entered timestamps that exceed the match duration or the commas may not have been entered correctly between timestamps. Please review and correct your input.");
          }
          return goalMinutes.map((minute, index) => ({
            id: Date.now() + index + Math.random() * 1000,
            minute: minute,
          }));
        } else {
          setIsInvalid(false);
        }
      }

      return goalMinutes.map((minute, index) => ({
        id: Date.now() + index + Math.random() * 1000,
        minute: minute,
      }));
    },
    [calculateMatchDuration, showValidationError, events, matchId, setIsInvalid]
  );

  const goals = useMemo(() => (Array.isArray(player.goals) ? player.goals : []), [player.goals]);
  const displayValue = useMemo(() => goalsToString(goals), [goals, goalsToString]);

  useEffect(() => {
    if (!isTyping && displayValue !== inputValue) {
      setInputValue(displayValue);
    }
  }, [displayValue, isTyping, inputValue]);

  const [isInitialized, setIsInitialized] = useState(false);
  useEffect(() => {
    if (!isInitialized) {
      setInputValue(displayValue);
      setIsInitialized(true);
    }
  }, [displayValue, isInitialized]);

  if (mode === "view") {
    return <span className="goals-cell__display">{displayValue}</span>;
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    let newInputValue = e.target.value;

    newInputValue = newInputValue.replace(/[^0-9,\s]/g, "");

    setInputValue(newInputValue);
    setIsTyping(true);

    const newGoals = stringToGoals(newInputValue);

    handlePlayerUpdate(playerIndex, "goals", newGoals);
  };

  const handleBlur = () => {
    setTimeout(() => {
      setIsTyping(false);
    }, 100);
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      setIsTyping(false);
      e.currentTarget.blur();
    }
    if (e.key === ",") {
      e.stopPropagation();
    }

    const invalidKeys = ["-", "+", "e", "E", "."];
    if (invalidKeys.includes(e.key)) {
      e.preventDefault();
    }
  };

  const isDisabled = !matchDuration;

  return (
    <input
      type="text"
      value={inputValue}
      placeholder="15, 45, 67"
      onChange={handleChange}
      onBlur={handleBlur}
      onKeyDown={handleKeyDown}
      className={'goals-cell__input'}
      disabled={isDisabled}
    />
  );
});

GoalsCell.displayName = "GoalsCell";

export default GoalsCell;
