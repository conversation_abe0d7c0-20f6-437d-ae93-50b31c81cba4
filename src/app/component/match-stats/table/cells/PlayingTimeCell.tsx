import { memo, useCallback } from "react";

interface PlayingTimeCellProps {
  value: string | number | undefined;
  playerIndex: number;
  mode: string;
  handlePlayerUpdate: (index: number, field: string, value: string | number) => void;
  events: any[];
  showValidationError: (message: string) => void;
  matchId: string;
}

const PlayingTimeCell = memo(({ value, playerIndex, mode, handlePlayerUpdate, events, showValidationError, matchId }: PlayingTimeCellProps) => {
  const displayValue = value ?? "";

  const calculateMatchDuration = useCallback(() => {
    if (events && events.length > 0) {
      const matchEvent = events.find((event: any) => event._id === matchId);
      if (matchEvent && matchEvent.startTime && matchEvent.endTime) {
        const startTime = new Date(matchEvent.startTime);
        const endTime = new Date(matchEvent.endTime);
        const durationMs = endTime.getTime() - startTime.getTime();
        const durationMinutes = Math.floor(durationMs / (1000 * 60));
        return durationMinutes;
      }
    }
    return 120;
  }, [events, matchId]);

  if (mode === "view") {
    return <span className="editable-cell__display">{displayValue}</span>;
  }

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    const invalidKeys = ["-", "+", "e", "E", "."];
    if (invalidKeys.includes(e.key)) {
      e.preventDefault();
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    let value = e.target.value;
    const numericValue = value.replace(/[^0-9]/g, "");
    if (numericValue !== value) {
      e.target.value = numericValue;
      value = numericValue;
    }

    const newValue = parseInt(value) || 0;

    if (value !== "" && events && matchId) {
      const matchDuration = calculateMatchDuration();
      if (newValue > matchDuration) {
        showValidationError("You have entered timestamps that exceed the match duration or the commas may not have been entered correctly between timestamps. Please review and correct your input.");
      }
    }

    handlePlayerUpdate(playerIndex, "playingTime", newValue);
  };

  const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    const newValue = parseInt(e.target.value) || 0;
    const matchDuration = calculateMatchDuration();

    if (newValue > matchDuration) {
      showValidationError("You have entered timestamps that exceed the match duration or the commas may not have been entered correctly between timestamps. Please review and correct your input.");
      e.target.value = "";
      handlePlayerUpdate(playerIndex, "playingTime", "");
      return;
    }
  };

  return (
    <input
      type="number"
      value={displayValue}
      onChange={handleChange}
      onKeyDown={handleKeyDown}
      onBlur={handleBlur}
      className="editable-cell__input"
      max={calculateMatchDuration()}
    />
  );
});

PlayingTimeCell.displayName = "PlayingTimeCell";

export default PlayingTimeCell;
