import { memo, useCallback, useState, useEffect } from "react";

interface EditableCellProps {
  value: string | number | undefined;
  playerIndex: number;
  field: string;
  type?: string;
  mode: string;
  handlePlayerUpdate: (index: number, field: string, value: string | number) => void;
  events?: any[];
  showValidationError?: (message: string) => void;
  matchId?: string;
  matchDuration?: number | null;
}

const EditableCell = memo(
  ({ value, playerIndex, field, type = "text", mode, handlePlayerUpdate, events, showValidationError, matchId, matchDuration }: EditableCellProps) => {
    const displayValue = value ?? "";
    const [isInvalid, setIsInvalid] = useState(false);

    const timeValidationFields = ["subOut", "subIn", "red", "yellow", "playingTime"];
    const needsTimeValidation = timeValidationFields.includes(field);

    const calculateMatchDuration = useCallback(() => {
      return matchDuration || null;
    }, [matchDuration]);

    // Re-validate when match duration changes
    useEffect(() => {
      if (needsTimeValidation && type === "number" && value && value !== "") {
        const numericValue = Number(value);
        const currentMatchDuration = calculateMatchDuration();

        if (currentMatchDuration && !isNaN(numericValue)) {
          if (numericValue > currentMatchDuration) {
            setIsInvalid(true);
          } else {
            setIsInvalid(false);
          }
        }
      }
    }, [matchDuration, value, needsTimeValidation, type, calculateMatchDuration]);

    if (mode === "view") {
      return <span className="editable-cell__display">{displayValue}</span>;
    }

    const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
      if (type === "number") {
        const invalidKeys = ["-", "+", "e", "E", "."];
        if (invalidKeys.includes(e.key)) {
          e.preventDefault();
        }
      }
    };

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      let newValue: string | number = e.target.value;

      if (!e.target.value || e.target.value === "") {
        setIsInvalid(false);
        handlePlayerUpdate(playerIndex, field, "");
        return;
      }

      if (type === "number") {
        const numericValue = e.target.value.replace(/[^0-9]/g, "");
        if (numericValue !== e.target.value) {
          e.target.value = numericValue;
          newValue = numericValue;
        }

        if (field === "goals" || field === "red" || field === "yellow") {
          newValue = parseInt(numericValue) || 0;
        } else {
          newValue = parseInt(numericValue) || 0;
        }

        if (field === "ratings") {
          const ratingValue = parseInt(numericValue);
          if (ratingValue > 10 || (ratingValue < 1 && numericValue !== "")) {
            setIsInvalid(true);
            if (showValidationError) {
              showValidationError("Rating must be between 1 and 10. Please enter a valid rating.");
            }
          } else {
            setIsInvalid(false);
          }
        }

        if (needsTimeValidation) {
          const matchDuration = calculateMatchDuration();
          if (matchDuration) {
            if (newValue > matchDuration) {
              setIsInvalid(true);
              if (showValidationError) {
                showValidationError("You have entered timestamps that exceed the match duration.");
              }
            } else {
              setIsInvalid(false);
            }
          }
        }
      }
      handlePlayerUpdate(playerIndex, field, newValue);
    };

    const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
      if (needsTimeValidation && type === "number" && e.target.value && e.target.value !== "") {
        const newValue = parseFloat(e.target.value);
        const matchDuration = calculateMatchDuration();

        if (matchDuration && newValue > matchDuration) {
          setIsInvalid(true);
          if (showValidationError) {
            showValidationError("You have entered timestamps that exceed the match duration.");
          }
          return;
        } else {
          setIsInvalid(false);
        }
      }
    };

    const getInputProps = () => {
      let props: any = {};

      if (needsTimeValidation && type === "number") {
        props.max = calculateMatchDuration();
        props.onBlur = handleBlur;
      }

      if (field === "ratings" && type === "number") {
        props.min = 1;
        props.max = 10;
      }

      return props;
    };

    const inputProps = getInputProps();

    const isDisabled = !matchDuration && needsTimeValidation;

    return (
      <input
        type={type}
        value={displayValue}
        onChange={handleChange}
        onKeyDown={handleKeyDown}
        className={`editable-cell__input ${isInvalid ? "editable-cell__input--invalid" : ""}`}
        disabled={isDisabled}
        {...inputProps}
      />
    );
  }
);

EditableCell.displayName = "EditableCell";

export default EditableCell;
