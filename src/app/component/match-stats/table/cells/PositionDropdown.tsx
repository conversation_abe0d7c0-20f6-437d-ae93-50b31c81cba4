import { memo, useCallback, useMemo } from "react";

interface Player {
  givenPosition?: string;
}

interface Position {
  _id: string;
  name: string;
  shortName?: string;
}

interface PositionDropdownProps {
  value: string | undefined;
  playerIndex: number;
  mode: string;
  players: Player[];
  positions: Position[];
  handlePlayerUpdate: (index: number, field: string, value: string) => void;
}

const PositionDropdown = memo(({ value, playerIndex, mode, players, positions, handlePlayerUpdate }: PositionDropdownProps) => {
  const currentPlayer = players[playerIndex];

  const extractShortName = useCallback((positionName: string) => {
    const match = positionName.match(/\(([^)]+)\)$/);
    return match ? match[1] : positionName;
  }, []);

  const findMatchingPosition = useCallback(
    (givenPos: string) => {
      if (!givenPos || !positions.length) return "";

      const exactMatch = positions.find((pos) => {
        const shortName = extractShortName(pos.name);
        return shortName.toLowerCase() === givenPos.toLowerCase() || pos.name.toLowerCase() === givenPos.toLowerCase();
      });

      if (exactMatch) {
        return extractShortName(exactMatch.name);
      }

      const partialMatch = positions.find((pos) => {
        const shortName = extractShortName(pos.name);
        return pos.name?.toLowerCase().includes(givenPos.toLowerCase()) || shortName.toLowerCase().includes(givenPos.toLowerCase());
      });

      return partialMatch ? extractShortName(partialMatch.name) : "";
    },
    [positions, extractShortName]
  );

  const currentPosition = useMemo(() => {
    if (value) {
      return value;
    }

    if (currentPlayer?.givenPosition) {
      const matchedPosition = findMatchingPosition(currentPlayer.givenPosition);
      if (matchedPosition) {
        return matchedPosition;
      }
      return currentPlayer.givenPosition;
    }

    return "";
  }, [value, currentPlayer?.givenPosition, findMatchingPosition]);

  const dropdownOptions = useMemo(() => {
    const uniquePositionsMap = new Map();

    positions.forEach((position) => {
      const shortName = extractShortName(position.name);
      if (!uniquePositionsMap.has(shortName.toLowerCase())) {
        uniquePositionsMap.set(shortName.toLowerCase(), {
          key: position._id,
          value: shortName,
          label: shortName,
          fullName: position.name,
        });
      }
    });

    const standardOptions = Array.from(uniquePositionsMap.values());

    if (currentPlayer?.givenPosition) {
      const givenPos = currentPlayer.givenPosition;
      const hasMatchingOption = standardOptions.some(
        (option) => option.value.toLowerCase() === givenPos.toLowerCase() || option.fullName.toLowerCase() === givenPos.toLowerCase()
      );

      if (!hasMatchingOption) {
        standardOptions.push({
          key: `custom-${givenPos}`,
          value: givenPos,
          label: givenPos,
          fullName: givenPos,
        });
      }
    }

    return standardOptions;
  }, [positions, currentPlayer?.givenPosition, extractShortName]);

  if (mode === "view") {
    return <span className="position-dropdown__display">{currentPosition || ""}</span>;
  }

  const handleChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    handlePlayerUpdate(playerIndex, "pos", e.target.value);
  };

  return (
    <select value={currentPosition} onChange={handleChange} className="position-dropdown__select">
      <option value="">Select Position</option>
      {dropdownOptions.map((option) => (
        <option key={option.key} value={option.value}>
          {option.label}
        </option>
      ))}
    </select>
  );
});

PositionDropdown.displayName = "PositionDropdown";

export default PositionDropdown;
