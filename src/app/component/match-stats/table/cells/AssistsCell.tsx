import { memo, useState, useCallback, useMemo, useEffect } from "react";

interface Player {
  assists?: string | number | Array<{ id: number; minute: number }>;
}

interface AssistsCellProps {
  player: Player;
  playerIndex: number;
  mode: string;
  handlePlayerUpdate: (index: number, field: string, value: any[]) => void;
  events?: any[];
  showValidationError?: (message: string) => void;
  matchId?: string;
  matchDuration?: number | null;
}

const AssistsCell = memo(({ player, playerIndex, mode, handlePlayerUpdate, events, showValidationError, matchId, matchDuration }: AssistsCellProps) => {
  const [inputValue, setInputValue] = useState("");
  const [isTyping, setIsTyping] = useState(false);
  const [isInvalid, setIsInvalid] = useState(false);

  const calculateMatchDuration = useCallback(() => {
    return matchDuration || null;
  }, [matchDuration]);

  const assistsToString = useCallback((assists: any) => {
    if (Array.isArray(assists)) {
      return assists
        .map((assist: any) => assist.minute)
        .sort((a: number, b: number) => a - b)
        .join(", ");
    }
    return "";
  }, []);

  const stringToAssists = useCallback(
    (str: string) => {
      if (!str || !str.trim()) {
        setIsInvalid(false);
        return [];
      }

      const assistMinutes = str
        .split(",")
        .map((minute) => minute.trim())
        .filter((minute) => minute !== "" && !isNaN(Number(minute)) && Number(minute) > 0)
        .map((minute) => Number(minute));

      const matchDuration = calculateMatchDuration();
      if (matchDuration) {
        const invalidMinutes = assistMinutes.filter((minute) => minute > matchDuration);
        if (invalidMinutes.length > 0) {
          setIsInvalid(true);
          if (showValidationError) {
            showValidationError("You have entered timestamps that exceed the match duration or the commas may not have been entered correctly between timestamps. Please review and correct your input.");
          }
          return assistMinutes.map((minute, index) => ({
            id: Date.now() + index + Math.random() * 1000,
            minute: minute,
          }));
        } else {
          setIsInvalid(false);
        }
      }

      return assistMinutes.map((minute, index) => ({
        id: Date.now() + index + Math.random() * 1000,
        minute: minute,
      }));
    },
    [calculateMatchDuration, showValidationError, events, matchId, setIsInvalid]
  );

  const assists = useMemo(() => (Array.isArray(player.assists) ? player.assists : []), [player.assists]);
  const displayValue = useMemo(() => assistsToString(assists), [assists, assistsToString]);

  useEffect(() => {
    if (!isTyping && displayValue !== inputValue) {
      setInputValue(displayValue);
    }
  }, [displayValue, isTyping, inputValue]);

  const [isInitialized, setIsInitialized] = useState(false);
  useEffect(() => {
    if (!isInitialized) {
      setInputValue(displayValue);
      setIsInitialized(true);
    }
  }, [displayValue, isInitialized]);

  if (mode === "view") {
    return <span className="goals-cell__display">{displayValue}</span>;
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    let newInputValue = e.target.value;

    newInputValue = newInputValue.replace(/[^0-9,\s]/g, "");

    setInputValue(newInputValue);
    setIsTyping(true);

    const newAssists = stringToAssists(newInputValue);

    handlePlayerUpdate(playerIndex, "assists", newAssists);
  };

  const handleBlur = () => {
    setTimeout(() => {
      setIsTyping(false);
    }, 100);
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      setIsTyping(false);
      e.currentTarget.blur();
    }
    if (e.key === ",") {
      e.stopPropagation();
    }

    const invalidKeys = ["-", "+", "e", "E", "."];
    if (invalidKeys.includes(e.key)) {
      e.preventDefault();
    }
  };

  const isDisabled = !matchDuration;

  return (
    <input
      type="text"
      value={inputValue}
      placeholder="20, 55, 78"
      onChange={handleChange}
      onBlur={handleBlur}
      onKeyDown={handleKeyDown}
      className={'goals-cell__input'}
      disabled={isDisabled}
    />
  );
});

AssistsCell.displayName = "AssistsCell";

export default AssistsCell;
