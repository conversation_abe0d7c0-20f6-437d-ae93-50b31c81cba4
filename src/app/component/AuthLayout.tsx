'use client';

import { useEffect } from 'react';
import { redirect } from 'next/navigation';
import { useDispatch, useSelector } from 'react-redux';
import { AppDispatch, RootState } from '@/redux/store';
import { LOGGED, NOTLOGGED } from '@/const/loginState';
import Loader from '@/app/component/Loader';
import { getLoginState } from '@/api/amplifyApi';
import { configureAmplify, setLoginState } from '@/redux/slices/authReducer';

const AuthLayout = ({ children }: { children: React.ReactNode }) => {
  const dispatch = useDispatch<AppDispatch>();

  const { loginState } = useSelector((state: RootState) => state?.auth);

  useEffect(() => {
    (async () => {
      await dispatch(configureAmplify());

      const userLoginState = await getLoginState();

      dispatch(setLoginState(userLoginState));
    })();
  }, []);

  useEffect(() => {
    if (loginState === NOTLOGGED) {
      redirect('/login');
    }
  }, [loginState]);

  return loginState === LOGGED ? children : <Loader />;
};

export default AuthLayout;
