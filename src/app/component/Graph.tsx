import { useEffect, useState, useMemo } from 'react';
import ReactEcharts from 'echarts-for-react';
import { useSelector } from 'react-redux';
import { RootState } from '@/redux/store';

interface ChartData {
  current: number;
  target: number;
  dateTime: string;
}

interface GraphProps {
  chartData: ChartData[];
}

type SeriesData = [string, number];

const currentLineColor = '#36d982';
const targetLineColor = '#1dc4d2';
const textColor = '#ffffff';
const axisColor = '#0e2434';

const Graph = ({ chartData }: GraphProps) => {
  const [xAxisName, setXAxisName] = useState('');

  const { dateRange } = useSelector((state: RootState) => state?.chart);

  useEffect(() => {
    const fromDate = formatDate(dateRange?.fromDate);
    const toDate = formatDate(dateRange?.toDate);

    setXAxisName(`${fromDate} - ${toDate}`);
  }, [dateRange]);

  useEffect(() => {
    const fromDate = formatDate(dateRange?.fromDate);
    const toDate = formatDate(dateRange?.toDate);

    setXAxisName(`${fromDate} - ${toDate}`);
  }, [dateRange]);

  const { currentStat, targetStat } = useMemo(() => {
    const categories: string[] = [];
    const currentStat: SeriesData[] = [];
    const targetStat: SeriesData[] = [];

    chartData.forEach((record) => {
      const time = record.dateTime;
      currentStat.push([time, record.current]);
      targetStat.push([time, record.target]);
    });

    return { categories, currentStat, targetStat };
  }, [chartData]);

  const formatDate = (timestamp: string) => {
    const dateObject = new Date(timestamp);
    const options: Intl.DateTimeFormatOptions = {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    };

    return dateObject.toLocaleDateString('en-US', options);
  };

  const option = {
    dataZoom: [
      {
        id: 'dataZoomX',
        type: 'inside',
        xAxisIndex: [0],
        filterMode: 'none',
        moveOnMouseWheel: true,
      },
    ],
    legend: {
      data: ['Target', 'Current'],
      align: 'left',
      right: 'auto',
      textStyle: {
        color: 'white',
      },
      itemGap: 30,
    },
    aria: {
      darkMode: true,
    },
    tooltip: {
      trigger: 'axis',
    },
    toolbox: {
      show: false,
    },
    xAxis: {
      type: 'time',
      boundaryGap: false,
      axisLine: {
        show: true,
        lineStyle: {
          color: axisColor,
          width: 1.5,
        },
      },
      axisLabel: {
        show: true,
        fontSize: 15,
        interval: 'auto',
        color: textColor,
        margin: 20,
        rotate: 30,
      },
    },
    yAxis: {
      type: 'value',
      show: true,
      splitNumber: 10,
      splitLine: {
        show: false,
      },
      axisLine: {
        show: true,
        lineStyle: {
          color: axisColor,
          width: 1.5,
        },
      },
      axisLabel: {
        show: true,
        fontSize: 17,
        color: textColor,
        margin: 12,
      },
    },
    series: [
      {
        name: 'Target',
        type: 'line',
        data: targetStat,
        symbolSize: 14,
        symbol: 'circle',
        color: targetLineColor,
        lineStyle: {
          type: 'dashed',
          color: targetLineColor,
        },
      },
      {
        name: 'Current',
        type: 'line',
        data: currentStat,
        symbolSize: 14,
        symbol: 'circle',
        color: currentLineColor,
        lineStyle: {
          type: 'dashed',
          color: currentLineColor,
        },
      },
    ],
  };

  return (
    <div className="graph-container">
      <ReactEcharts className="echart" option={option} />
      <div className="date-range">{xAxisName}</div>
    </div>
  );
};

export default Graph;
