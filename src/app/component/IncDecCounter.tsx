import { useEffect, useState } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faPlus, faMinus } from '@fortawesome/free-solid-svg-icons';
import { useSelector } from 'react-redux';
import { RootState } from '@/redux/store';

interface Level {
  start: number;
  end: number;
  colorCode: string;
}

interface Activity {
  _id: string;
  activity: string;
  maximum: number;
  minimum: number;
  levels: Level[];
}

interface IncDecCounterProps {
  value: number | null;
  sportsProfileId: string;
  handleChange: (value: number, sportsProfileId: string) => void;
  disabled: boolean;
}

const defaultValue = 10;

const IncDecCounter = ({
  value,
  sportsProfileId,
  handleChange,
  disabled,
}: IncDecCounterProps) => {
  const [selectedActivity, setSelectedActivity] = useState<Activity | null>(
    null
  );

  const { activities, selectedActivityId } = useSelector(
    (state: RootState) => state?.dashboard
  );

  useEffect(() => {
    const matchingActivity: Activity | undefined = activities.find(
      (activity: Activity) => activity._id === selectedActivityId
    );

    setSelectedActivity(matchingActivity || null);
  }, [activities, selectedActivityId]);

  useEffect(() => {
    if (value === null && !disabled) {
      handleChange(defaultValue, sportsProfileId);
    }
  }, [disabled]);

  const getColorForValue = (value: number): string => {
    if (!selectedActivity) return '';

    const level = selectedActivity.levels.find(
      ({ start, end }) => value >= start && value <= end
    );

    return level ? level.colorCode : '';
  };

  const incNum = () => {
    if (selectedActivity && (value || 0) < selectedActivity.maximum) {
      handleChange(1, sportsProfileId);
    }
  };

  const decNum = () => {
    if (selectedActivity && (value || 0) > selectedActivity.minimum) {
      handleChange(-1, sportsProfileId);
    }
  };

  const handleNumberChange = (event: { target: { value: string } }) => {
    const newValue = parseInt(event.target.value, 10);
    const diffValue = newValue - (value || 0);

    if (
      selectedActivity &&
      newValue >= selectedActivity.minimum &&
      newValue <= selectedActivity.maximum
    ) {
      handleChange(diffValue, sportsProfileId);
    }
  };

  const inputColor = getColorForValue(value || 0);

  return (
    <div className="inc-dec-container">
      <button disabled={disabled} type="button" onClick={decNum}>
        <FontAwesomeIcon icon={faMinus} />
      </button>
      <input
        type="text"
        disabled={disabled}
        style={{ color: inputColor }}
        value={value ?? '-'}
        onChange={handleNumberChange}
      />
      <button disabled={disabled} type="button" onClick={incNum}>
        <FontAwesomeIcon icon={faPlus} />
      </button>
    </div>
  );
};

export default IncDecCounter;
