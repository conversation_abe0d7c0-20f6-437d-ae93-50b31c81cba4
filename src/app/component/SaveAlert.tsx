import { useState } from 'react';
import { Alert } from 'react-bootstrap';

interface SaveAlertProps {
  type: 'success' | 'danger';
  message: string;
  onClose: () => void;
}

const SaveAlert = ({ type, message, onClose }: SaveAlertProps) => {
  const [show, setShow] = useState(true);

  setTimeout(() => {
    setShow(false);
    onClose();
  }, 5000);

  return (
    <Alert show={show} variant={type}>
      {message}
    </Alert>
  );
};

export default SaveAlert;
