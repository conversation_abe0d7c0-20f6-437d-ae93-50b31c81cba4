'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useDispatch, useSelector } from 'react-redux';
import Spinner from 'react-bootstrap/Spinner';
import { AppDispatch, RootState } from '@/redux/store';
import { savePlayerStat } from '@/redux/slices/playerReducer';
import { setDateRange, getChartData } from '@/redux/slices/chartReducer';
import {
  setSelectedActivityId,
  setIsActivitiesLoading,
} from '@/redux/slices/dashboardReducer';
import LeaveAlertModal from '@/app/component/LeaveAlertModal';
import SaveAlert from '@/app/component/SaveAlert';

import { FaRegCalendar } from 'react-icons/fa';
interface SideNavBarProps {
  selectedCategory: string;
}

const SideNavBar = ({ selectedCategory }: SideNavBarProps) => {
  const { categories } = useSelector((state: RootState) => state?.category);

  const dispatch = useDispatch<AppDispatch>();
  const router = useRouter();

  const [isAlertShow, setIsAlertShow] = useState(false);
  const [changedCategoryName, setChangedCategoryName] = useState('');
  const [isSaveBtnDisable, setIsSaveBtnDisable] = useState(true);
  const [isSuccessAlert, setIsSuccessAlert] = useState(false);
  const [isFailAlert, setIsFailAlert] = useState(false);

  const { isSaveButtonLoading, updatedPlayersStat } = useSelector(
    (state: RootState) => state?.player
  );

  const { isChart, dateRange } = useSelector(
    (state: RootState) => state?.chart
  );

  useEffect(() => {
    setIsSaveBtnDisable(!updatedPlayersStat?.length);
  }, [updatedPlayersStat]);

  useEffect(() => {
    const currentDate = new Date();
    const defaultFromDate = new Date(currentDate);

    defaultFromDate.setMonth(currentDate.getMonth() - 1);

    const defaultDateRange = {
      fromDate: defaultFromDate.toISOString(),
      toDate: currentDate.toISOString(),
    };

    dispatch(setDateRange(defaultDateRange));
  }, []);

  const handleSelectCategory = (categoryName: string) => {
    dispatch(setIsActivitiesLoading(true));
    dispatch(setSelectedActivityId(''));

    if (isSaveBtnDisable) {
      navigateToDashboard(categoryName);
    } else {
      setChangedCategoryName(categoryName);
      setIsAlertShow(true);
    }
  };

  const navigateToDashboard = (categoryName: string) => {
    router.push(`/dashboard/${categoryName}`);
  };

  const handleCloseModal = () => {
    setIsAlertShow(false);
  };

  const handleYes = () => {
    navigateToDashboard(changedCategoryName);
    handleCloseModal();
  };

  const navButtons = () => {
    return categories?.map((category: { name: string }, index: number) => {
      const categoryName = category.name.toLowerCase();
      const isActive = categoryName === selectedCategory ? 'active' : '';

      return (
        <button
          key={index}
          className={`nav-button ${isActive}`}
          onClick={() => handleSelectCategory(categoryName)}
        >
          {category.name}
        </button>
      );
    });
  };

  const handleSaveStat = async () => {
    const saveResponse = await dispatch(savePlayerStat());

    if (saveResponse.payload) {
      setIsSuccessAlert(true);
    } else {
      setIsFailAlert(true);
    }
  };

  const handleDateChange = (event: {
    target: { name: string; value: string };
  }) => {
    const { name, value } = event.target;

    if (value) {
      const date = new Date(value + 'T00:00:00.000Z');

      if (validateDate(name, date)) {
        const newDateRange = {
          ...dateRange,
          [name]: date.toISOString(),
        };

        dispatch(setDateRange(newDateRange));

        if (newDateRange.fromDate && newDateRange.toDate) {
          dispatch(getChartData());
        }
      }
    } else {
      const newDateRange = {
        ...dateRange,
        [name]: '',
      };

      dispatch(setDateRange(newDateRange));
    }
  };

  const validateDate = (name: string, date: Date) => {
    let isValied = false;

    switch (name) {
      case 'fromDate':
        const endDate = new Date(dateRange?.toDate);
        if (!dateRange?.toDate || date <= endDate) {
          isValied = true;
        }
        break;

      case 'toDate':
        const startDate = new Date(dateRange?.fromDate);
        if (!dateRange?.fromDate || date >= startDate) {
          isValied = true;
        }
        break;
    }

    return isValied;
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');

    return `${year}-${month}-${day}`;
  };

  const chartForm = () => {
    return (
      <div className="chart-form mb-5">
        <div className="horizontal-line"></div>
        <h5 className="title-label-2 mt-2">Please select a date range</h5>
        <form action="">
          <div className="input-container">
            <div className="date-input-container">
              <input
                type={dateRange.fromDate ? 'date' : 'text'}
                onFocus={(e) => (e.target.type = 'date')}
                id="fromDate"
                value={formatDate(dateRange?.fromDate)}
                placeholder="Start Date"
                name="fromDate"
                onChange={handleDateChange}
                max={formatDate(dateRange?.toDate)}
              />
              <span className="icon">
                <FaRegCalendar />
              </span>
            </div>
          </div>
          <div className="input-container">
            <div className="date-input-container">
              <input
                type={dateRange.toDate ? 'date' : 'text'}
                onFocus={(e) => (e.target.type = 'date')}
                id="toDate"
                value={formatDate(dateRange?.toDate)}
                name="toDate"
                placeholder="End Date"
                onChange={handleDateChange}
                min={formatDate(dateRange?.fromDate)}
              />
              <span className="icon">
                <FaRegCalendar />
              </span>
            </div>
          </div>
        </form>
      </div>
    );
  };

  return (
    <div className="side-bar">
      <div className="logo-container">
        <img src="/images/logo.png" alt="" className="logo me-2" />
        <h4 className="title-label mt-1">Koach</h4>
      </div>
      <h5 className="title-label-2 mt-5">Please select a parameter</h5>
      <div className="button-container">{navButtons()}</div>
      <div>
        {isChart ? (
          chartForm()
        ) : (
          <div className="mt-5">
            <div className="save-alert">
              {isSuccessAlert && (
                <SaveAlert
                  type="success"
                  message="Player stats saved successfully!"
                  onClose={() => setIsSuccessAlert(false)}
                />
              )}
              {isFailAlert && (
                <SaveAlert
                  type="danger"
                  message="Saving player stats failed!"
                  onClose={() => setIsFailAlert(false)}
                />
              )}
            </div>
            {isSaveButtonLoading ? (
              <div className="d-flex justify-content-center save-spinner">
                <Spinner animation="border" />
              </div>
            ) : (
              <button
                disabled={isSaveBtnDisable}
                className="save-btn"
                onClick={handleSaveStat}
              >
                Save Changes
              </button>
            )}
          </div>
        )}
      </div>
      <LeaveAlertModal
        show={isAlertShow}
        onHide={handleCloseModal}
        onYes={handleYes}
      />
    </div>
  );
};

export default SideNavBar;
