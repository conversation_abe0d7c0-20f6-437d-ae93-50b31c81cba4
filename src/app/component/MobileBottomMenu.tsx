import { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import Spinner from 'react-bootstrap/Spinner';
import { AppDispatch, RootState } from '@/redux/store';
import { savePlayerStat } from '@/redux/slices/playerReducer';
import { setDateRange, getChartData } from '@/redux/slices/chartReducer';
import SaveAlert from '@/app/component/SaveAlert';
import { CiCalendar } from 'react-icons/ci';

const MobileBottomMenu = () => {
  const dispatch = useDispatch<AppDispatch>();

  const [isSaveBtnDisable, setIsSaveBtnDisable] = useState(true);
  const [isSuccessAlert, setIsSuccessAlert] = useState(false);
  const [isFailAlert, setIsFailAlert] = useState(false);

  const { isSaveButtonLoading, updatedPlayersStat } = useSelector(
    (state: RootState) => state?.player
  );

  const { isChart, dateRange } = useSelector(
    (state: RootState) => state?.chart
  );

  useEffect(() => {
    setIsSaveBtnDisable(!updatedPlayersStat?.length);
  }, [updatedPlayersStat]);

  const handleSaveStat = async () => {
    const saveResponse = await dispatch(savePlayerStat());

    setIsSuccessAlert(saveResponse.payload === true);
    setIsFailAlert(saveResponse.payload !== true);
  };

  const handleDateChange = (event: {
    target: { name: string; value: string };
  }) => {
    const { name, value } = event.target;

    if (value) {
      const date = new Date(value + 'T00:00:00.000Z');

      if (validateDate(name, date)) {
        const newDateRange = {
          ...dateRange,
          [name]: date.toISOString(),
        };

        dispatch(setDateRange(newDateRange));

        if (newDateRange.fromDate && newDateRange.toDate) {
          dispatch(getChartData());
        }
      }
    } else {
      const newDateRange = {
        ...dateRange,
        [name]: '',
      };

      dispatch(setDateRange(newDateRange));
    }
  };

  const validateDate = (name: string, date: Date) => {
    let isValied = false;

    switch (name) {
      case 'fromDate':
        const endDate = new Date(dateRange?.toDate);
        if (!dateRange?.toDate || date <= endDate) {
          isValied = true;
        }
        break;

      case 'toDate':
        const startDate = new Date(dateRange?.fromDate);
        if (!dateRange?.fromDate || date >= startDate) {
          isValied = true;
        }
        break;
    }

    return isValied;
  };

  const chartForm = () => {
    return (
      <div className="chart-form mb-5">
        <h5 className="title-label mt-2">Please select a date range</h5>
        <form action="">
          <div className="input-container">
            <div className="date-input-container">
              <input
                type={dateRange.fromDate ? 'date' : 'text'}
                onFocus={(e) => (e.target.type = 'date')}
                id="fromDate"
                value={dateRange?.fromDate.split('T')[0]}
                placeholder="Start Date"
                name="fromDate"
                onChange={handleDateChange}
                max={dateRange?.toDate.split('T')[0]}
              />
              <span className="icon">
                <CiCalendar />
              </span>
            </div>
          </div>
          <div className="input-container">
            <div className="date-input-container">
              <input
                type={dateRange.toDate ? 'date' : 'text'}
                onFocus={(e) => (e.target.type = 'date')}
                id="toDate"
                value={dateRange?.toDate.split('T')[0]}
                name="toDate"
                placeholder="End Date"
                onChange={handleDateChange}
                min={dateRange?.fromDate.split('T')[0]}
              />
              <span className="icon">
                <CiCalendar />
              </span>
            </div>
          </div>
        </form>
      </div>
    );
  };

  return (
    <div className="mobile-form">
      {isChart ? (
        chartForm()
      ) : (
        <div className="mt-5">
          <div className="save-alert">
            {isSuccessAlert && (
              <SaveAlert
                type="success"
                message="Player stats saved successfully!"
                onClose={() => setIsSuccessAlert(false)}
              />
            )}
            {isFailAlert && (
              <SaveAlert
                type="danger"
                message="Saving player stats failed!"
                onClose={() => setIsFailAlert(false)}
              />
            )}
          </div>
          {isSaveButtonLoading ? (
            <div className="d-flex justify-content-center save-spinner">
              <Spinner animation="border" />
            </div>
          ) : (
            <button
              disabled={isSaveBtnDisable}
              className="save-btn"
              onClick={handleSaveStat}
            >
              Save Changes
            </button>
          )}
        </div>
      )}
    </div>
  );
};

export default MobileBottomMenu;
