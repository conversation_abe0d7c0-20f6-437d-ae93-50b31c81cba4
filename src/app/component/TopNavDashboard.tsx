'use client';

import { useState, useEffect, MouseEvent, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { useDispatch, useSelector } from 'react-redux';
import { FaSignOutAlt } from 'react-icons/fa';
import { MdOutlineKeyboardBackspace } from 'react-icons/md';
import { AppDispatch, RootState } from '@/redux/store';
import { signOut, setLoginState } from '@/redux/slices/authReducer';
import { PENDING } from '@/const/loginState';
import { generatePlaceholderImage } from '@/utils/imageUtils';
import { setIsChart, setChartData } from '@/redux/slices/chartReducer';
import { setPlayers } from '@/redux/slices/playerReducer';
import { setIsCategoryLoading } from '@/redux/slices/categoryReducer';

const TopNavDashboard = () => {
  const dispatch = useDispatch<AppDispatch>();
  const router = useRouter();

  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [profileImageUrl, setProfileImage] = useState('');
  const [teamName, setTeamName] = useState('');
  const [isLogoutVisible, setLogoutVisible] = useState(false);

  const { isChart } = useSelector((state: RootState) => state?.chart);

  const logoutRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (window && window.localStorage) {
      const storeTeamName = localStorage.getItem('teamName') || '';
      const storeFirstName = localStorage.getItem('firstName') || '';
      const storeLastName = localStorage.getItem('lastName') || '';
      const storeProfileImageUrl =
        localStorage.getItem('profileImageUrl') || '';

      setTeamName(storeTeamName);
      setFirstName(storeFirstName);
      setLastName(storeLastName);
      setProfileImage(storeProfileImageUrl);
    }
  }, []);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        logoutRef.current &&
        !logoutRef.current.contains(event.target as Node)
      ) {
        setLogoutVisible(false);
      }
    };

    window.addEventListener('click', handleClickOutside as any);

    return () => {
      window.removeEventListener('click', handleClickOutside as any);
    };
  }, [isLogoutVisible]);

  const toggleLogout = () => {
    setLogoutVisible(!isLogoutVisible);
  };

  const handleLogout = async (event: MouseEvent<HTMLButtonElement>) => {
    event.preventDefault();

    try {
      dispatch(setLoginState(PENDING));
      dispatch(signOut());
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  const handleHistoryBack = () => {
    if (isChart) {
      dispatch(setIsChart(false));
      dispatch(setChartData([]));
    } else {
      dispatch(setIsCategoryLoading(true));
      dispatch(setPlayers([]));
      router.push(`/`);
    }
  };

  const profileImgUrl =
    profileImageUrl !== 'null'
      ? profileImageUrl
      : generatePlaceholderImage(firstName);

  return (
    <div className="top-navbar-dashboard">
      <button onClick={handleHistoryBack} className="back-btn">
        <h3>
          <MdOutlineKeyboardBackspace />
        </h3>
      </button>
      <div className="team-name">{teamName}</div>
      <div ref={logoutRef} className="user-profile" onClick={toggleLogout}>
        <div className="user-name me-1 d-fexl">
          <p className="greeting">Welcome</p>
          <span>{`${firstName} ${lastName !== 'null' ? lastName : ''}`}</span>
        </div>
        <div className="user-avatar">
          <img src={profileImgUrl} alt="User Avatar" />
        </div>

        {isLogoutVisible && (
          <div className="logout-btn-container">
            <button className="logout-btn" onClick={handleLogout}>
              <FaSignOutAlt /> Log out
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default TopNavDashboard;
