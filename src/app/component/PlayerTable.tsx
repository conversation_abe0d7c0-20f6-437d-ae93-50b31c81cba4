'use client';

import { useEffect, useState, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { FiSearch } from 'react-icons/fi';
import { IoStatsChart } from 'react-icons/io5';
import { AppDispatch, RootState } from '@/redux/store';
import { generatePlaceholderImage } from '@/utils/imageUtils';
import IncDecCounter from '@/app/component/IncDecCounter';
import CheckBox from '@/app/component/CheckBox';
import Pagination from '@/app/component/Pagination';
import LeaveAlertModal from '@/app/component/LeaveAlertModal';
import { setSlectedPlayerId, setIsChart } from '@/redux/slices/chartReducer';
import {
  getPlayers,
  setCurrentState,
  setTargetState,
  setCurrentPage,
  setUpdatedPlayersStat,
  getPlayersByName,
} from '@/redux/slices/playerReducer';

const CURRENT = 'current';
const TARGET = 'target';

const PlayerTable = () => {
  const dispatch = useDispatch<AppDispatch>();

  const [displaySide, setDisplaySide] = useState(CURRENT);
  const [editablePlayers, setEditablePlayers] = useState<string[]>([]);
  const [searchName, setSearchName] = useState('');
  const [isAlertShow, setIsAlertShow] = useState(false);

  const {
    players,
    playerStat,
    updatedPlayersStat,
    totalPlayers,
    currentPage,
    hasSearchedByName,
    searchedName,
  } = useSelector((state: RootState) => state?.player);

  const totalPages = useMemo(() => {
    return Math.ceil(totalPlayers / 20);
  }, [totalPlayers]);

  useEffect(() => {
    setEditablePlayers([]);
  }, [playerStat]);

  useEffect(() => {
    setSearchName(searchedName);
  }, [searchedName]);

  const handlePagination = (pageNumber: number) => {
    dispatch(setCurrentPage(pageNumber));

    if (hasSearchedByName) {
      dispatch(getPlayersByName(searchName));
    } else {
      dispatch(getPlayers());
    }
  };

  const handleSlideBtnClick = () => {
    setDisplaySide((displaySide) =>
      displaySide === CURRENT ? TARGET : CURRENT
    );
  };

  const handleSearchInputChange = (event: { target: { value: string } }) => {
    const { value } = event.target;

    setSearchName(value);
  };

  const handlePlayerSearch = () => {
    dispatch(setCurrentPage(1));
    dispatch(getPlayersByName(searchName));
    setEditablePlayers([]);
  };

  const handleCheckBoxChange = (
    name: string,
    checked: boolean,
    playerId: string
  ) => {
    if (checked) {
      setEditablePlayers((prevPlayers) => [...prevPlayers, playerId]);
    } else {
      setEditablePlayers((prevPlayers) =>
        prevPlayers.filter((sportsProfileId) => sportsProfileId !== playerId)
      );

      const newUpdatedPlayerStat = updatedPlayersStat.filter(
        (stat) => stat.sportsProfileId !== playerId
      );

      dispatch(setUpdatedPlayersStat(newUpdatedPlayerStat));
    }
  };

  const handleCurrentChange = (value: Number, sportsProfileId: string) => {
    dispatch(setCurrentState({ value, sportsProfileId }));
  };

  const handleTargetChange = (value: Number, sportsProfileId: string) => {
    dispatch(setTargetState({ value, sportsProfileId }));
  };

  const handleClickChart = (playerId: string) => {
    dispatch(setSlectedPlayerId(playerId));
    if (updatedPlayersStat?.length) {
      setIsAlertShow(true);
    } else {
      dispatch(setIsChart(true));
    }
  };

  const handleCloseModal = () => {
    setIsAlertShow(false);
  };

  const handleYes = () => {
    dispatch(setUpdatedPlayersStat([]));
    dispatch(setIsChart(true));

    handleCloseModal();
  };

  const mobileSearchBar = () => (
    <div className="mobile-search-bar mt-3">
      <div className="row no-gutters search-input-db">
        <input
          type="text"
          placeholder="Player name"
          className="form-control"
          id="playerSearch"
          name="playerSearch"
          onChange={handleSearchInputChange}
          defaultValue={searchName}
        />
        <button
          onClick={handlePlayerSearch}
          className="btn btn-base search-icon"
        >
          <FiSearch />
        </button>
      </div>
    </div>
  );

  const tableRows = () => {
    return players?.map(
      (
        player: {
          firstName: string;
          lastName: string;
          profileImageUrl: string;
          sportsProfileId: string;
        },
        index: number
      ) => {
        const { firstName, lastName, profileImageUrl, sportsProfileId } =
          player;

        const profileImgUrl =
          profileImageUrl || generatePlaceholderImage(firstName);

        const matchingStat =
          updatedPlayersStat?.find(
            (stat: { sportsProfileId: string }) =>
              stat.sportsProfileId === sportsProfileId
          ) ??
          playerStat?.find(
            (stat: { sportsProfileId: string }) =>
              stat.sportsProfileId === sportsProfileId
          ) ??
          null;

        const current = matchingStat?.current ?? null;
        const target = matchingStat?.target ?? null;
        const isEditable = editablePlayers.includes(sportsProfileId);

        return (
          <tr key={index}>
            <td className="ps-2 playerCheckBox">
              <CheckBox
                name="active"
                value={isEditable}
                onChange={handleCheckBoxChange}
                playerId={sportsProfileId}
                checkPage="dashboard-check"
              />
            </td>
            <td className="player-avatar-td">
              <div className="player-profile">
                <div className="user-avatar">
                  <img src={profileImgUrl} alt={firstName + ' ' + lastName} />
                </div>
              </div>
            </td>
            <td className="palyerName">
              {firstName} {lastName}
            </td>
            <td
              className={`text-center inc-dec-td ${
                displaySide !== CURRENT ? 'hide-column' : ''
              }`}
            >
              <IncDecCounter
                value={current}
                sportsProfileId={sportsProfileId}
                handleChange={handleCurrentChange}
                disabled={!isEditable}
              />
            </td>
            <td
              className={`text-center inc-dec-td ${
                displaySide !== TARGET ? 'hide-column' : ''
              }`}
              style={{ paddingLeft: '10px' }}
            >
              <IncDecCounter
                value={target}
                sportsProfileId={sportsProfileId}
                handleChange={handleTargetChange}
                disabled={!isEditable}
              />
            </td>
            <td
              className={`graph-icon ps-1 ${
                displaySide !== TARGET ? 'hide-column' : ''
              }`}
            >
              <button onClick={() => handleClickChart(sportsProfileId)}>
                <IoStatsChart />
              </button>
            </td>
          </tr>
        );
      }
    );
  };

  const desktopTableHead = () => (
    <tr className="search-bar-container">
      <th colSpan={3} scope="col">
        <div className="search-bar">
          <div className="row no-gutters search-input-db">
            <div className="sarch-bar-input">
              <input
                type="text"
                placeholder="Player name"
                className="form-control"
                id="playerSearch"
                name="playerSearch"
                onChange={handleSearchInputChange}
                defaultValue={searchName}
                onKeyDown={(event) => {
                  if (event.key === 'Enter') {
                    handlePlayerSearch();
                  }
                }}
              />
              <button
                onClick={handlePlayerSearch}
                className="btn btn-base search-icon"
              >
                <FiSearch />
              </button>
            </div>
          </div>
        </div>
      </th>
      <th className="text-center" scope="col">
        Current
      </th>
      <th className="text-center" scope="col">
        Target
      </th>
      <th>&nbsp;&nbsp;</th>
    </tr>
  );

  const mobileTableHead = () => (
    <tr className="mobile-table-head">
      <th colSpan={3} scope="col" className="mobile-player-col">
        <button onClick={handleSlideBtnClick} className="mobile-slide-btn">
          {displaySide === CURRENT ? '>' : '<'}
        </button>
        Player Name
      </th>
      <th
        className={`text-center ${
          displaySide !== CURRENT ? 'hide-column' : ''
        }`}
        scope="col"
      >
        Current
      </th>
      <th
        className={`text-center ${displaySide !== TARGET ? 'hide-column' : ''}`}
        scope="col"
        colSpan={2}
      >
        Target
      </th>
    </tr>
  );

  const table = () => (
    <>
      <div className="players-table-container">
        {mobileSearchBar()}
        <table className="players-table bg-transparent">
          <thead>
            {desktopTableHead()}
            {mobileTableHead()}
          </thead>
          <tbody>{tableRows()}</tbody>
        </table>
      </div>
      {totalPages > 1 && (
        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          handlePageClick={handlePagination}
        />
      )}
      <LeaveAlertModal
        show={isAlertShow}
        onHide={handleCloseModal}
        onYes={handleYes}
      />
    </>
  );

  return table();
};

export default PlayerTable;
