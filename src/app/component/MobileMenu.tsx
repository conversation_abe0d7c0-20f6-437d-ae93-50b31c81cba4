'use client';

import { useState } from 'react';
import { useSelector } from 'react-redux';
import Link from 'next/link';
import { RiArrowDownSLine } from 'react-icons/ri';
import { RootState } from '@/redux/store';

interface MobileMenuProps {
  selectedCategory: string;
}

const MobileMenu = ({ selectedCategory }: MobileMenuProps) => {
  const { categories } = useSelector((state: RootState) => state?.category);

  const [isClickd, setIsClicked] = useState(false);

  const selectedCategoryName =
    selectedCategory.substring(0, 1).toUpperCase() +
    selectedCategory.substring(1);

  const options = () => {
    return categories?.map((category: { name: string }, index: number) => {
      const categoryName = category.name.toLowerCase();
      const isActive = categoryName === selectedCategory ? 'active' : '';

      return (
        <Link
          key={index}
          className={`nav-button ${isActive}`}
          href={`/dashboard/${categoryName}`}
        >
          {category.name}
        </Link>
      );
    });
  };

  return (
    <>
      <div className="dropdown mb-3">
        <h5 className="lable">Please select a parameter</h5>
        <button
          className="dropdown-button"
          onClick={() => setIsClicked(!isClickd)}
        >
          {selectedCategoryName} <RiArrowDownSLine />
        </button>
        {isClickd && <div className="dropdown-content">{options()}</div>}
      </div>
    </>
  );
};

export default MobileMenu;
