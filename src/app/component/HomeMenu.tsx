import React from "react";
import { useRouter } from 'next/navigation';


const HomeMenu = () => {
  const router = useRouter();
  

  function handleSelectTeam(path: string) {
   router.push(`/${path}`);
  }

  return (
    <div className="mainContent"
    >
      <div className="container"
      >
        <div className="leftContent">
          <h2 className="second-title">
            Welcome to
          </h2>
          <h1 className="main-title">
            KOACH
          </h1>
          <p className="second-title">
            A single platform for Coaches and Admins            
          </p>
        </div>

        <div className="rightContent">
          <div className="tile"
            onClick={() => {
              handleSelectTeam("iap");
            }}
          >
            <h3 className="tileText">
              IAP
            </h3>
          </div>
          <div className="tile"
            onClick={() => {
            handleSelectTeam("match-stats");
          }}
          >
            <h3 className="tileText">
              MATCH STATS
            </h3>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HomeMenu;