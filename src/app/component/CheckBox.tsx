interface CheckBoxProps {
  onChange: (
    name: string,
    checked: boolean,
    playerId: string,
    checkPage: string
  ) => void;
  name: string;
  value: boolean;
  playerId?: string;
  checkPage?: string;
}

const CheckBox = ({
  onChange,
  name,
  value,
  playerId = '',
  checkPage = '',
}: CheckBoxProps) => {
  const handleChange = (event: {
    target: { name: string; checked: boolean };
  }) => {
    const { name, checked } = event.target;

    onChange(name, checked, playerId, checkPage);
  };

  return (
    <>
      <label className={`checkbox ${value ? 'active-check' : ''}`}>
        <input
          hidden
          type="checkbox"
          id={name}
          name={name}
          aria-describedby={name}
          checked={value}
          onChange={handleChange}
        />
        <div
          className={`checkmark ${checkPage} ${!value ? 'unchecked' : ''}`}
        ></div>
      </label>
    </>
  );
};

export default CheckBox;
