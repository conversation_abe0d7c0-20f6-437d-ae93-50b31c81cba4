'use client';

import React, { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useDispatch, useSelector } from 'react-redux';
import Spinner from 'react-bootstrap/Spinner';
import { AppDispatch, RootState } from '@/redux/store';
import TopNavBar from '@/app/component/TopNavBar';
import { getTeams, setIsPageLoading } from '@/redux/slices/teamReducer';
import { getCategories } from '@/redux/slices/categoryReducer';
import { setCurrentPage, setPlayers } from '@/redux/slices/playerReducer';
import { setIsChart } from '@/redux/slices/chartReducer';

export default function HomeBody() {
  const dispatch = useDispatch<AppDispatch>();
  const router = useRouter();

  const { teams, isTeamLoading } = useSelector(
    (state: RootState) => state?.team
  );
  const { categories } = useSelector((state: RootState) => state?.category);

  useEffect(() => {
    dispatch(setIsChart(false));
    dispatch(setIsPageLoading(false));
    dispatch(getTeams());
    dispatch(getCategories());
  }, []);

  const handleSelectTeam = (team: { _id: string; teamName: string }) => {
    dispatch(setIsPageLoading(true));
    dispatch(setPlayers([]));

    localStorage.setItem('teamId', team._id);
    localStorage.setItem('teamName', team.teamName);

    dispatch(setCurrentPage(1));

    if (categories.length) {
      const firstCategory = categories[0]?.name?.toLowerCase();

      router.push(`/dashboard/${firstCategory}`);
    }
  };

  const teamCards = () => {
    if (isTeamLoading) {
      return (
        <div className='mt-4 w-100 d-flex justify-content-center'>
          <Spinner animation='border' />
        </div>
      );
    } else if (!teams.length) {
      return <div className='error'>No Teams Available</div>;
    }

    return teams?.map((team, index) => (
      <div className='team-card-box' key={index}>
        <div
          className='team-card'
          onClick={() => {
            handleSelectTeam(team);
          }}
        >
          <div className='card-body'>
            <h5 className='card-title'>{team.teamName}</h5>
          </div>
        </div>
      </div>
    ));
  };

  return (
    <div className='container-fluid'>
      <div className='row'>
        <div className='col-md-12'>
          <TopNavBar />
          <div className='team-container'>
            <img src='/images/logo.png' alt='' className='logo' />
            <h4 className='title-label mt-1'>Koach</h4>
            <h4 className='title-label-2 mt-1'>PLEASE SELECT A TEAM</h4>
            <div className='container-fluid mb-5'>
              <div className='team-cards'>{teamCards()}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
