import { Modal, Button } from 'react-bootstrap';

interface AlertModalProps {
  show: boolean;
  onHide: () => void;
  onYes: () => void;
}

const LeaveAlertModal = ({ show, onHide, onYes }: AlertModalProps) => {
  return (
    <Modal show={show} onHide={onHide} className="alert-modal">
      <Modal.Header closeButton>
        <Modal.Title>Confirmation</Modal.Title>
      </Modal.Header>
      <Modal.Body>
        There are unsaved changes! do you want to leave the page?
      </Modal.Body>
      <Modal.Footer>
        <Button variant="secondary" onClick={onHide}>
          stay
        </Button>
        <Button variant="primary" className="yes-btn" onClick={onYes}>
          leave
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

export default LeaveAlertModal;
