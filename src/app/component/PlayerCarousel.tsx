import { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { IoIosArrowBack, IoIosArrowForward } from 'react-icons/io';
import { generatePlaceholderImage } from '@/utils/imageUtils';
import { RootState } from '@/redux/store';

interface Player {
  firstName: string;
  lastName: string;
  profileImageUrl: string;
  isAvailable: boolean;
  sportsProfileId: string;
}

interface PlayerCarouselProps {
  players: Player[];
  onSelect: (selectedPlayer: string) => void;
}

const PlayerCarousel = ({ players, onSelect }: PlayerCarouselProps) => {
  const [startIndex, setStartIndex] = useState(0);
  const [playersPerPage, setPlayersPerPage] = useState(
    players.length >= 2 ? 2 : 1
  );

  const [currentPlayers, setCurrentPlayers] = useState(
    players.slice(startIndex, startIndex + playersPerPage)
  );

  const { slectedPlayerId } = useSelector((state: RootState) => state?.chart);

  const totalPlayers = Math.ceil(players.length);
  const screenWidth = window.innerWidth;
  const disableButtons = totalPlayers <= playersPerPage;

  useEffect(() => {
    let itemsPerPage = playersPerPage;

    if (screenWidth >= 1200 && players.length >= 4) {
      itemsPerPage = 4;
    } else if (screenWidth >= 768 && players.length >= 3) {
      itemsPerPage = 3;
    }

    const playerIndex = players.findIndex(
      (player) => player.sportsProfileId === slectedPlayerId
    );

    setPlayersPerPage(itemsPerPage);
    setStartIndex(playerIndex);
  }, []);

  const handleNextPage = () => {
    setStartIndex((prevStartIndex) => (prevStartIndex + 1) % totalPlayers);
  };

  const handlePrevPage = () => {
    setStartIndex(
      (prevStartIndex) => (prevStartIndex - 1 + totalPlayers) % totalPlayers
    );
  };

  useEffect(() => {
    const newCurrentPlayers = Array.from(
      { length: playersPerPage },
      (_, index) => {
        const itemIndex = (startIndex + index) % players.length;
        return players[itemIndex];
      }
    );

    setCurrentPlayers(newCurrentPlayers);
  }, [playersPerPage, startIndex]);

  const playerButtons = () => {
    return currentPlayers.map(
      (
        player: {
          firstName: string;
          lastName: string;
          profileImageUrl: string;
          isAvailable: boolean;
          sportsProfileId: string;
        },
        index: number
      ) => {
        const { firstName, lastName, profileImageUrl, sportsProfileId } =
          player;

        const active = sportsProfileId === slectedPlayerId ? 'active' : '';

        return (
          <button
            key={index}
            onClick={() => onSelect(player.sportsProfileId)}
            className={`player-btn ${active}`}
          >
            <div className="user-avatar">
              <img
                src={profileImageUrl || generatePlaceholderImage(firstName)}
              />
            </div>
            <span className="player-name">
              {firstName} {lastName}
            </span>
          </button>
        );
      }
    );
  };

  return (
    <div className="player-carousel-container row">
      <div className="col-1">
        <button
          onClick={handlePrevPage}
          className="control-button"
          disabled={disableButtons}
        >
          <IoIosArrowBack />
        </button>
      </div>
      <div className="col-10">
        <div className="player-buttons-container">{playerButtons()}</div>
      </div>
      <div className="col-1">
        <button
          onClick={handleNextPage}
          className="control-button"
          disabled={disableButtons}
        >
          <IoIosArrowForward />
        </button>
      </div>
    </div>
  );
};

export default PlayerCarousel;
