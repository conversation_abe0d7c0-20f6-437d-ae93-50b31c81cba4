"use client";

import { useEffect, useRef, useState } from "react";
import { useRouter } from "next/navigation";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "@/redux/store";
import { getTeams } from "@/redux/slices/teamReducer";
import { getEvents, setSelectedTeamFromDashbord, setselectedMonthFromDashbord } from "@/redux/slices/eventReducer";
import {
  downloadReportwithURL
} from "@/redux/slices/matchReducer";
import { Spinner } from "react-bootstrap";
import { FiSearch, FiArrowLeft } from "react-icons/fi";
import { MONTH_NAMES, DAY_NAMES } from "@/const/matchStats";

const MatchDashboard = () => {
  const dispatch = useDispatch<AppDispatch>();
  const router = useRouter();
  const { teams, isTeamLoading } = useSelector((state: RootState) => state?.team);
  const { events, isEventLoading, selectedMonthFromDashBord, selectedTeamFromDashbord } = useSelector((state: RootState) => state?.event);
  const [selectedTeam, setSelectedTeam] = useState<{
    _id: string;
    teamName: string;
  }>();
  const [searchTeam, setSearchTeam] = useState("");
  const currentMonth = MONTH_NAMES[new Date().getMonth()];
  const [selectedMonth, setSelectedMonth] = useState(currentMonth);
  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    dispatch(getTeams());
    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, []);

  useEffect(() => {
    if (teams) {
      selectedTeamFromDashbord ? setSelectedTeam(selectedTeamFromDashbord) : setSelectedTeam(teams[0]);
    }
  }, [teams, selectedTeamFromDashbord]);

  useEffect(() => {
    selectedMonthFromDashBord && setSelectedMonth(selectedMonthFromDashBord);
  }, [selectedMonthFromDashBord]);

  useEffect(() => {
    if (selectedTeam && selectedMonth) {
      getEventsHandler(selectedTeam._id, selectedMonth);
    }
  }, [selectedTeam, selectedMonth]);

  const handleSelectEvent = (path: string, event?: any) => {
    if (event && event?.concluded && !event?.submittedByWeb) {
      window.open(event.reportUrl, "_blank", "noopener,noreferrer");
    } else {
      if (event && selectedTeam) {
        const opponentIds = event.opponentId;
        const teamId = event.teamId;
        const matchId = event._id;
        const teamName = selectedTeam.teamName;
        dispatch(setselectedMonthFromDashbord(selectedMonth));
        dispatch(setSelectedTeamFromDashbord(selectedTeam));
        const queryParams = new URLSearchParams({
          teamId,
          matchId,
          teamName,
          ...(opponentIds && { opponentIds }),
        });

        router.push(`/match-stats/${path}?${queryParams.toString()}`);
      }
    }
  };

  const handleSelectTeam = (team: { _id: string; teamName: string }) => {
    setSelectedTeam(team);
  };

  const handleTeamSearch = () => {
    dispatch(getTeams({ searchKey: searchTeam }));
  };

  const handleBackClick = () => {
    router.back();
  };

  const handleTeamSearchKey = (searchKey: string) => {
    setSearchTeam(searchKey);
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }
    if (searchKey === "") {
      searchTimeoutRef.current = setTimeout(() => {
        dispatch(getTeams());
      }, 500);
    } else {
      searchTimeoutRef.current = setTimeout(() => {
        dispatch(getTeams({ searchKey }));
      }, 500);
    }
  };

  const getEventsHandler = (teamId: string, month: string) => {
    if (!teamId || !month) return;
    const currentYear = new Date().getFullYear();
    const monthIndex = MONTH_NAMES.indexOf(month);
    const startDate = new Date(currentYear, monthIndex, 1, 0, 0, 0, 0);
    const endDate = new Date(currentYear, monthIndex + 1, 0, 23, 59, 59, 999);
    const startDateFormatted = startDate.toISOString();
    const endDateFormatted = endDate.toISOString();
    dispatch(
      getEvents({
        teamId,
        startDate: startDateFormatted,
        endDate: endDateFormatted,
      })
    );
  };

  const isEnableViewReport = (event: any) => {
    const startDate = new Date(event?.startTime);
    const currentDate = new Date();
    const twoWeeksAgo = new Date();
    twoWeeksAgo.setDate(currentDate.getDate() - 14);
    const isWithinTwoWeek = startDate >= twoWeeksAgo;
    return isWithinTwoWeek ? event?.concluded : true;
  };

  const formatDate = (startTime: string) => {
    if (!startTime) return "";

    const date = new Date(startTime);
    const dayName = DAY_NAMES[date.getDay()];
    const monthName = MONTH_NAMES[date.getMonth()];
    const day = date.getDate();
    const getOrdinalSuffix = (day: number) => {
      if (day > 3 && day < 21) return "th";
      switch (day % 10) {
        case 1:
          return "st";
        case 2:
          return "nd";
        case 3:
          return "rd";
        default:
          return "th";
      }
    };

    const formattedDate = `${monthName} ${day}${getOrdinalSuffix(day)}, ${dayName}`;

    return formattedDate;
  };

  const formatTime = (startTime: string) => {
    if (!startTime) return "";

    const date = new Date(startTime);
    const hours = date.getHours();
    const minutes = date.getMinutes();
    const ampm = hours >= 12 ? "PM" : "AM";
    const displayHours = hours % 12 || 12;
    const displayMinutes = minutes.toString().padStart(2, "0");

    const formattedTime = `${displayHours}:${displayMinutes}${ampm}`;

    return formattedTime;
  };

  const handleFileDownload = (reportName: string, reportUrl : string) => {
    dispatch(downloadReportwithURL({reportUrl,reportName}));
  };

  const teamView = () => {
    if (isTeamLoading) {
      return (
        <div className="match-dashboard__loading">
          <Spinner animation="border" />
        </div>
      );
    } else if (!teams.length) {
      return <div className="error">No Teams Available</div>;
    }

    return (
      <div className="match-dashboard__team-list">
        {teams.map((teamData) => (
          <button
            key={teamData._id}
            onClick={() => handleSelectTeam(teamData)}
            className={`match-dashboard__team-button ${selectedTeam?._id === teamData._id ? "match-dashboard__team-button--selected" : ""}`}
          >
            {teamData.teamName}
          </button>
        ))}
      </div>
    );
  };

  const eventView = () => {
    if (isEventLoading) {
      return (
        <div className="match-dashboard__loading">
          <Spinner animation="border" />
        </div>
      );
    } else if (!events.length) {
      return <div className="error">No Events Available</div>;
    }

    return (
      <div className="match-dashboard__matches-grid">
        {events.map((event) => (
          <div key={event._id} className="match-dashboard__match-card">
            <h3 className="match-dashboard__match-card__title">{event.name}</h3>
            <p className="match-dashboard__match-card__competition">
              {event?.tournament?.name}
            </p>

            <div className="match-dashboard__match-card__details">
              <div className="match-dashboard__match-card__detail-row">
                <span className="match-dashboard__match-card__detail-row-label">
                  Date:
                </span>
                <span className="match-dashboard__match-card__detail-row-value">
                  {formatDate(event.startTime)}
                </span>
              </div>
              <div className="match-dashboard__match-card__detail-row">
                <span className="match-dashboard__match-card__detail-row-label">
                  Time:
                </span>
                <span className="match-dashboard__match-card__detail-row-value">
                  {formatTime(event.startTime)}
                </span>
              </div>
              <div className="match-dashboard__match-card__detail-row">
                <span className="match-dashboard__match-card__detail-row-label">
                  Location:
                </span>
                <span className="match-dashboard__match-card__detail-row-value">
                  {event.location?.name}
                </span>
              </div>
            </div>

            <button
              className={`match-dashboard__match-card__action-button ${
                isEnableViewReport(event)
                  ? "match-dashboard__match-card__action-button--report"
                  : "match-dashboard__match-card__action-button--stats"
              }`}
              onClick={() => {
                handleSelectEvent(
                  isEnableViewReport(event) ? "report" : "forms",
                  event
                );
              }}
            >
              {isEnableViewReport(event) ? event?.submittedByWeb ? "View Report" : "Aktive Report" : "Enter Match Stats"}
            </button>
            {isEnableViewReport(event) && (
              <button
                className="match-dashboard__match-card__action-button match-dashboard__match-card__action-button--report-download"
                disabled={event?.reportUrl == null}
                onClick={()=>handleFileDownload(event.name,event?.reportUrl)}
              >
                Download Report
              </button>
            )}
          </div>
        ))}
      </div>
    );
  };

  return (
    <main className="match-dashboard">
      <div className="match-dashboard-left">
        <div className="match-dashboard__back-button" onClick={handleBackClick}>
          <FiArrowLeft />
          <span>Back</span>
        </div>
        <div className="logoArea">
          <img src="/images/koach-vertical.png" alt="" className="dashboard-logo" />
        </div>
        <div className="optionsArea">
          <div className="match-dashboard__sidebar">
            <div className="match-dashboard__search">
              <input
                type="text"
                placeholder="Search Team"
                value={searchTeam}
                onChange={(e) => handleTeamSearchKey(e.target.value)}
                className="match-dashboard__search-input"
              />
              <div className="match-dashboard__search-icon" onClick={handleTeamSearch}>
                <FiSearch />
              </div>
            </div>

            <div className="match-dashboard__team-section">
              <h3>Select Team</h3>
              {teamView()}
            </div>
          </div>
        </div>
      </div>
      <div className="match-dashboard-right">
        <h1 className="teamName">{selectedTeam?.teamName}</h1>
        <div className="match-dashboard__main">
          <div className="match-dashboard__month-filters">
            {MONTH_NAMES.map((month) => (
              <button
                key={month}
                onClick={() => setSelectedMonth(month)}
                className={`match-dashboard__month-button ${selectedMonth === month ? "match-dashboard__month-button--selected" : ""}`}
              >
                {month}
              </button>
            ))}
          </div>
          {eventView()}
        </div>
      </div>
    </main>
  );
};

export default MatchDashboard;
