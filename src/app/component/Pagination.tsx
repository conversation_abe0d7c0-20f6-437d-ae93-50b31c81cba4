'use client';

import { useState, useEffect } from 'react';
import { IoIosArrowBack, IoIosArrowForward } from 'react-icons/io';
import { HiDotsHorizontal } from 'react-icons/hi';

interface PaginationProps {
  currentPage: number;
  totalPages: number;
  handlePageClick: (pageNumber: number) => void;
}

const desktopScreenSize = 1200;
const tabletScreenSize = 768;
const desktopPageNeighbours = 5;
const tabletPageNeighbours = 4;

const Pagination = ({
  currentPage,
  totalPages,
  handlePageClick,
}: PaginationProps) => {
  const [pageNeighbours, setPageNeighbours] = useState(2);

  useEffect(() => {
    const screenWidth = window.innerWidth;

    if (screenWidth >= desktopScreenSize) {
      setPageNeighbours(desktopPageNeighbours);
    } else if (screenWidth >= tabletScreenSize) {
      setPageNeighbours(tabletPageNeighbours);
    }
  }, []);

  const renderPageButtons = () => {
    const buttons = [];
    const startPage = Math.max(1, currentPage - pageNeighbours);
    const endPage = Math.min(totalPages, currentPage + pageNeighbours);

    if (currentPage > 1) {
      buttons.push(
        <button key="prev" onClick={() => handlePageClick(currentPage - 1)}>
          <IoIosArrowBack />
        </button>
      );
    }

    // First page and ellipses
    if (startPage > 1) {
      buttons.push(
        <button
          className="pagination-btn"
          key={1}
          onClick={() => handlePageClick(1)}
        >
          1
        </button>
      );

      if (startPage > 2) {
        buttons.push(
          <button key="ellipsis1" disabled className="ellipsis">
            <HiDotsHorizontal />
          </button>
        );
      }
    }

    for (let i = startPage; i <= endPage; i++) {
      buttons.push(
        <button
          key={i}
          onClick={() => handlePageClick(i)}
          className={`${i === currentPage ? 'active' : ''} pagination-btn`}
        >
          {i}
        </button>
      );
    }

    // Last page and ellipses
    if (endPage < totalPages) {
      if (endPage < totalPages - 1) {
        buttons.push(
          <button key="ellipsis2" disabled className="ellipsis">
            <HiDotsHorizontal />
          </button>
        );
      }

      buttons.push(
        <button
          key={totalPages}
          className="pagination-btn"
          onClick={() => handlePageClick(totalPages)}
        >
          {totalPages}
        </button>
      );
    }

    if (currentPage < totalPages) {
      buttons.push(
        <button key="next" onClick={() => handlePageClick(currentPage + 1)}>
          <IoIosArrowForward />
        </button>
      );
    }

    return buttons;
  };

  return <div className="pagination pb-2">{renderPageButtons()}</div>;
};

export default Pagination;
