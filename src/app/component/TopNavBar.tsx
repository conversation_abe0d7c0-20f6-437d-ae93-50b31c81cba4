'use client';

import { useState, useEffect, useRef } from 'react';
import { useDispatch } from 'react-redux';
import { FaSignOutAlt } from 'react-icons/fa';
import { AppDispatch } from '@/redux/store';
import { signOut, setLoginState } from '@/redux/slices/authReducer';
import { PENDING } from '@/const/loginState';
import { generatePlaceholderImage } from '@/utils/imageUtils';

const TopNavBar = () => {
  const dispatch = useDispatch<AppDispatch>();
  const logoutRef = useRef<HTMLDivElement>(null);

  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [profileImageUrl, setProfileImage] = useState('');
  const [isLogoutVisible, setLogoutVisible] = useState(false);

  useEffect(() => {
    if (window && window.localStorage) {
      const storeFirstName = localStorage.getItem('firstName') || '';
      const storeLastName = localStorage.getItem('lastName') || '';
      const storeProfileImageUrl =
        localStorage.getItem('profileImageUrl') || '';

      setFirstName(storeFirstName);
      setLastName(storeLastName);
      setProfileImage(storeProfileImageUrl);
    }
  }, []);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        logoutRef.current &&
        !logoutRef.current.contains(event.target as Node)
      ) {
        setLogoutVisible(false);
      }
    };

    window.addEventListener('click', handleClickOutside);

    return () => {
      window.removeEventListener('click', handleClickOutside);
    };
  }, [logoutRef]);

  const toggleLogout = () => {
    setLogoutVisible(!isLogoutVisible);
  };

  const handleLogout = async () => {
    try {
      dispatch(setLoginState(PENDING));
      dispatch(signOut());
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  const profileImg =
    profileImageUrl !== 'null'
      ? profileImageUrl
      : generatePlaceholderImage(firstName);

  return (
    <div className="top-navbar" ref={logoutRef}>
      <div className="user-profile" onClick={toggleLogout}>
        <div className="user-name me-1">
          <p className="greeting">Welcome</p>
          <span>{`${firstName} ${lastName !== 'null' ? lastName : ''}`}</span>
        </div>
        <div className="user-avatar">
          <img src={profileImg} alt="User Avatar" />
        </div>
        {isLogoutVisible && (
          <div className="logout-btn-container">
            <button className="logout-btn" onClick={handleLogout}>
              <FaSignOutAlt /> Log out
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default TopNavBar;
