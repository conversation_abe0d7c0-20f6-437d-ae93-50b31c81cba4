'use client';

import { useSelector } from 'react-redux';
import { RootState } from '@/redux/store';
import Loader from '@/app/component/Loader';
import AuthLayout from '@/app/component/AuthLayout';
import DashboardBody from './DashboardBody';

interface CategoryPageProps {
  params: {
    category: string;
  };
}

const CategoryPage = ({ params }: CategoryPageProps) => {
  const selectedCategory = params.category;

  const { isCategoryLoading } = useSelector(
    (state: RootState) => state?.category
  );

  const { isChartLoading } = useSelector((state: RootState) => state?.chart);

  const { isActivitiesLoading } = useSelector(
    (state: RootState) => state?.dashboard
  );

  const { isPlayersLoading, isStatLoading } = useSelector(
    (state: RootState) => state?.player
  );

  const isPageLoading =
    isPlayersLoading ||
    isActivitiesLoading ||
    isCategoryLoading ||
    isStatLoading ||
    isChartLoading;

  const loader = () => (isPageLoading ? <Loader /> : null);

  return (
    <AuthLayout>
      {loader()}
      <DashboardBody selectedCategory={selectedCategory} />
    </AuthLayout>
  );
};

export default CategoryPage;
