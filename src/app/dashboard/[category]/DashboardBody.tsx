'use client';

import { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { AppDispatch, RootState } from '@/redux/store';
import SideNavBar from '@/app/component/SideNavBar';
import MobileMenu from '@/app/component/MobileMenu';
import TopNavDashboard from '@/app/component/TopNavDashboard';
import PlayerCarousel from '@/app/component/PlayerCarousel';
import Graph from '@/app/component/Graph';
import MobileBottomMenu from '@/app/component/MobileBottomMenu';
import DashboardTabs from '@/app/dashboard/DashboardTabs';
import PlayerTable from '@/app/component/PlayerTable';
import { setSlectedPlayerId, getChartData } from '@/redux/slices/chartReducer';
import { getPlayers } from '@/redux/slices/playerReducer';
import { getActivities } from '@/redux/slices/dashboardReducer';
import {
  getCategories,
  selectCategoryIdByName,
  setSelectedCategoryId,
} from '@/redux/slices/categoryReducer';

interface DashboardBodyProps {
  selectedCategory: string;
}

const DashboardBody = ({ selectedCategory }: DashboardBodyProps) => {
  const dispatch = useDispatch<AppDispatch>();

  const { categories } = useSelector((state: RootState) => state?.category);

  const { isChart, dateRange, chartData } = useSelector(
    (state: RootState) => state?.chart
  );

  const { players } = useSelector((state: RootState) => state?.player);

  useEffect(() => {
    if (!players.length) {
      dispatch(getPlayers());
    }

    if (!categories.length) {
      dispatch(getCategories());
    }
  }, []);

  const selectedCategoryId = useSelector(
    selectCategoryIdByName(selectedCategory)
  );

  useEffect(() => {
    if (selectedCategoryId) {
      dispatch(setSelectedCategoryId(selectedCategoryId));
      dispatch(getActivities({ categoryId: selectedCategoryId }));
    }
  }, [selectedCategoryId]);

  const handlePlayerSelect = (selectedPlayerId: string) => {
    dispatch(setSlectedPlayerId(selectedPlayerId));

    if (dateRange.fromDate && dateRange.toDate) {
      dispatch(getChartData());
    }
  };

  const chart = () => (
    <>
      <div className="mt-3">
        <PlayerCarousel players={players} onSelect={handlePlayerSelect} />
      </div>
      <div className="chart">
        {chartData && chartData.length ? (
          <Graph chartData={chartData} />
        ) : (
          <h3>No graphical data available</h3>
        )}
      </div>
    </>
  );

  return (
    <div className="container-fluid">
      <div className="row flex-nowrap">
        <div className="col-auto col-md-3 col-xl-2 px-sm-2 px-0 side-navbar">
          <SideNavBar selectedCategory={selectedCategory} />
        </div>
        <div className="col dashboard-body">
          <div className="row">
            <TopNavDashboard />
          </div>
          <div className="row">
            <MobileMenu selectedCategory={selectedCategory} />
          </div>
          <div className="row">
            <DashboardTabs selectedCategory={selectedCategory} />
          </div>
          <div className="row">{isChart ? chart() : <PlayerTable />}</div>
          <div className="row">
            <MobileBottomMenu />
          </div>
        </div>
      </div>
    </div>
  );
};

export default DashboardBody;
