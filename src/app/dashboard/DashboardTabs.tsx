import { useEffect, useState } from 'react';
import { AppDispatch, RootState } from '@/redux/store';
import { useDispatch, useSelector } from 'react-redux';
import { setSelectedActivityId } from '@/redux/slices/dashboardReducer';
import { getPlayerStat } from '@/redux/slices/playerReducer';
import { selectCategoryIdByName } from '@/redux/slices/categoryReducer';
import { getChartData } from '@/redux/slices/chartReducer';
import LeaveAlertModal from '@/app/component/LeaveAlertModal';

interface DashboardTabsProps {
  selectedCategory: string;
}

const DashboardTabs = ({ selectedCategory }: DashboardTabsProps) => {
  const dispatch = useDispatch<AppDispatch>();

  const [isAlertShow, setIsAlertShow] = useState(false);
  const [changedActivityName, setChangedActivtyName] = useState('');

  const { activities, selectedActivityId } = useSelector(
    (state: RootState) => state?.dashboard
  );

  const { isChart, dateRange } = useSelector(
    (state: RootState) => state?.chart
  );

  const { players, updatedPlayersStat } = useSelector(
    (state: RootState) => state?.player
  );

  const categoryId = useSelector(selectCategoryIdByName(selectedCategory));

  const handleSelectActivity = (criteriaId: string) => {
    if (!updatedPlayersStat?.length) {
      dispatch(setSelectedActivityId(criteriaId));
    } else {
      setChangedActivtyName(criteriaId);
      setIsAlertShow(true);
    }
  };

  const handleCloseModal = () => {
    setIsAlertShow(false);
  };

  const handleYes = () => {
    dispatch(setSelectedActivityId(changedActivityName));
    handleCloseModal();
  };

  useEffect(() => {
    if (players.length && selectedActivityId) {
      if (isChart) {
        if (dateRange.fromDate && dateRange.toDate) {
          dispatch(getChartData());
        }
      } else {
        const sportsProfileIds = players.map(
          (player) => player.sportsProfileId
        );

        dispatch(
          getPlayerStat({
            categoryId,
            criteriaId: selectedActivityId,
            sportsProfileIds,
          })
        );
      }
    }
  }, [selectedActivityId, players, isChart]);

  const activityTabs = () =>
    activities?.map(
      (activity: { activity: string; _id: string }, index: number) => {
        const activityName = activity.activity;
        const isActive = activity._id === selectedActivityId ? 'active' : '';

        return (
          <button
            key={index}
            className={`tab-btn ${isActive}`}
            onClick={() => {
              handleSelectActivity(activity._id);
            }}
          >
            {activityName}
          </button>
        );
      }
    );

  return (
    <div className="activity-container">
      <div className="button-container">{activityTabs()}</div>
      <div className="horizontal-line"></div>
      <LeaveAlertModal
        show={isAlertShow}
        onHide={handleCloseModal}
        onYes={handleYes}
      />
    </div>
  );
};

export default DashboardTabs;
